# RouteCategory API Documentation

## Overview
The RouteCategory API provides complete CRUD operations for managing route categories used in the homepage "Popular Routes" section. It supports multi-language content management through `groupId` and `languageId` parameters.

## Base URL
```
/api/v1/routecategories
```

## Authentication
All endpoints require proper authentication headers.

## Endpoints

### 1. GET Route Categories
**Endpoint:** `POST /api/v1/routecategories`

**Description:** Retrieve route categories with optional filtering by groupId, languageId, status, and search term.

**Method:** POST  
**Content-Type:** application/json

#### Request Body
```json
{
  "groupId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa7",
  "status": 1,
  "searchTerm": "popular",
  "page": 1,
  "pageSize": 10
}
```

#### Request Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| groupId | UUID | No | Filter by group ID for multilingual content |
| languageId | UUID | No | Filter by specific language |
| status | Integer | No | Filter by status (0=Draft, 1=Active, 2=Passive) |
| searchTerm | String | No | Search in title and description fields |
| page | Integer | No | Page number (default: 1) |
| pageSize | Integer | No | Items per page (default: 10000) |

#### Response (200 OK)
```json
{
  "data": [
    {
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "projectId": "3fa85f64-5717-4562-b3fc-2c963f66afa1",
      "userId": "3fa85f64-5717-4562-b3fc-2c963f66afa2",
      "groupId": "3fa85f64-5717-4562-b3fc-2c963f66afa3",
      "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa4",
      "status": 1,
      "title": "Popular Destinations",
      "description": "Discover the most popular travel destinations",
      "icon": "fas fa-plane",
      "deleted": false,
      "createdAt": "2025-07-05T10:30:00Z",
      "updatedAt": "2025-07-05T10:30:00Z"
    }
  ],
  "isSuccessful": true,
  "message": null,
  "errors": null,
  "page": 1,
  "pageSize": 10,
  "totalCount": 1,
  "totalPages": 1,
  "hasNextPage": false,
  "hasPreviousPage": false
}
```

#### Response (400 Bad Request)
```json
{
  "data": null,
  "isSuccessful": false,
  "message": "Invalid request parameters",
  "errors": ["Page must be greater than 0"]
}
```

---

### 2. GET Route Category by ID
**Endpoint:** `GET /api/v1/routecategories/{id}`

**Description:** Retrieve a specific route category by its unique identifier.

**Method:** GET

#### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | UUID | Yes | The unique identifier of the route category |

#### Response (200 OK)
```json
{
  "data": {
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "projectId": "3fa85f64-5717-4562-b3fc-2c963f66afa1",
    "userId": "3fa85f64-5717-4562-b3fc-2c963f66afa2",
    "groupId": "3fa85f64-5717-4562-b3fc-2c963f66afa3",
    "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa4",
    "status": 1,
    "title": "Popular Destinations",
    "description": "Discover the most popular travel destinations",
    "icon": "fas fa-plane",
    "deleted": false,
    "createdAt": "2025-07-05T10:30:00Z",
    "updatedAt": "2025-07-05T10:30:00Z"
  },
  "isSuccessful": true,
  "message": null,
  "errors": null
}
```

#### Response (404 Not Found)
```json
{
  "data": null,
  "isSuccessful": false,
  "message": "RouteCategory not found for this id 3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "errors": null
}
```

---

### 3. CREATE Route Categories
**Endpoint:** `POST /api/v1/routecategories/create`

**Description:** Create one or more route categories. Multiple language versions can be created by providing different languageId values with the same groupId.

**Method:** POST  
**Content-Type:** application/json

#### Request Body
```json
{
  "routeCategories": [
    {
      "status": 0,
      "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa4",
      "groupId": "3fa85f64-5717-4562-b3fc-2c963f66afa3",
      "title": "Popular Destinations",
      "description": "Discover the most popular travel destinations around the world",
      "icon": "fas fa-plane"
    },
    {
      "status": 0,
      "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa5",
      "groupId": "3fa85f64-5717-4562-b3fc-2c963f66afa3",
      "title": "Popüler Destinasyonlar",
      "description": "Dünyanın en popüler seyahat destinasyonlarını keşfedin",
      "icon": "fas fa-plane"
    }
  ]
}
```

#### Request Body Schema
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| routeCategories | Array | Yes | Array of route category objects to create |
| routeCategories[].status | Integer | Yes | Status (0=Draft, 1=Active, 2=Passive) |
| routeCategories[].languageId | UUID | Yes | Language identifier |
| routeCategories[].groupId | UUID | Yes | Group identifier for multilingual content |
| routeCategories[].title | String | Yes | Category title (max 255 characters) |
| routeCategories[].description | String | Yes | Category description |
| routeCategories[].icon | String | No | Icon class or identifier |

#### Response (200 OK)
```json
{
  "data": true,
  "isSuccessful": true,
  "message": null,
  "errors": null
}
```

#### Response (400 Bad Request)
```json
{
  "data": false,
  "isSuccessful": false,
  "message": "Title is required, Description is required",
  "errors": [
    "Title is required for route category at index 0",
    "Description cannot be empty for route category at index 1"
  ]
}
```

---

### 4. UPDATE Route Categories
**Endpoint:** `PUT /api/v1/routecategories`

**Description:** Update existing route categories.

**Method:** PUT  
**Content-Type:** application/json

#### Request Body
```json
{
  "routeCategories": [
    {
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "status": 1,
      "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa4",
      "groupId": "3fa85f64-5717-4562-b3fc-2c963f66afa3",
      "title": "Most Popular Destinations",
      "description": "Discover the most popular travel destinations worldwide",
      "icon": "fas fa-globe"
    }
  ]
}
```

#### Request Body Schema
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| routeCategories | Array | Yes | Array of route category objects to update |
| routeCategories[].id | UUID | Yes | Unique identifier of the category to update |
| routeCategories[].status | Integer | Yes | Status (0=Draft, 1=Active, 2=Passive) |
| routeCategories[].languageId | UUID | Yes | Language identifier |
| routeCategories[].groupId | UUID | Yes | Group identifier for multilingual content |
| routeCategories[].title | String | Yes | Category title (max 255 characters) |
| routeCategories[].description | String | Yes | Category description |
| routeCategories[].icon | String | No | Icon class or identifier |

#### Response (200 OK)
```json
{
  "data": true,
  "isSuccessful": true,
  "message": null,
  "errors": null
}
```

#### Response (400 Bad Request)
```json
{
  "data": false,
  "isSuccessful": false,
  "message": "RouteCategory not found for id: 3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "errors": null
}
```

---

### 5. DELETE Route Categories
**Endpoint:** `DELETE /api/v1/routecategories`

**Description:** Soft delete route categories by their IDs. Returns the count of successfully deleted items.

**Method:** DELETE  
**Content-Type:** application/json

#### Request Body
```json
{
  "ids": [
    "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "3fa85f64-5717-4562-b3fc-2c963f66afa7"
  ]
}
```

#### Request Body Schema
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| ids | Array[UUID] | Yes | Array of route category IDs to delete |

#### Response (200 OK)
```json
{
  "data": 2,
  "isSuccessful": true,
  "message": null,
  "errors": null
}
```

#### Response (400 Bad Request - Invalid IDs)
```json
{
  "data": 0,
  "isSuccessful": false,
  "message": "Invalid RouteCategory IDs: 3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "errors": null
}
```

#### Response (404 Not Found)
```json
{
  "data": 0,
  "isSuccessful": false,
  "message": "No route categories found to delete",
  "errors": null
}
```

---

## Data Models

### RouteCategory Response Model
```json
{
  "id": "UUID",
  "projectId": "UUID",
  "userId": "UUID", 
  "groupId": "UUID",
  "languageId": "UUID",
  "status": "Integer (0=Draft, 1=Active, 2=Passive)",
  "title": "String",
  "description": "String",
  "icon": "String",
  "deleted": "Boolean",
  "createdAt": "DateTime (ISO 8601)",
  "updatedAt": "DateTime (ISO 8601)"
}
```

### Status Values
| Value | Description |
|-------|-------------|
| 0 | Draft |
| 1 | Active |
| 2 | Passive |

---

## Multi-Language Support

### Grouping Content by Language
- Use the same `groupId` for different language versions of the same route category
- Provide different `languageId` values for each language
- Filter by `groupId` to get all language versions
- Filter by `languageId` to get specific language version

### Example Multi-Language Creation
```json
{
  "routeCategories": [
    {
      "groupId": "same-group-id-for-all-languages",
      "languageId": "english-language-id",
      "title": "Popular Destinations",
      "description": "Discover popular destinations"
    },
    {
      "groupId": "same-group-id-for-all-languages", 
      "languageId": "turkish-language-id",
      "title": "Popüler Destinasyonlar",
      "description": "Popüler destinasyonları keşfedin"
    }
  ]
}
```

---

## Error Handling

All endpoints return consistent error responses with the following structure:

```json
{
  "data": null,
  "isSuccessful": false,
  "message": "Error description",
  "errors": ["Detailed error messages"]
}
```

### Common HTTP Status Codes
- **200**: Success
- **400**: Bad Request (validation errors, invalid parameters)
- **404**: Not Found (resource doesn't exist)
- **500**: Internal Server Error

---

## Usage Examples

### Get All Active Categories for English Language
```bash
curl -X POST "https://api.example.com/api/v1/routecategories" \
  -H "Content-Type: application/json" \
  -d '{
    "languageId": "english-language-id",
    "status": 1,
    "page": 1,
    "pageSize": 20
  }'
```

### Create Multilingual Category
```bash
curl -X POST "https://api.example.com/api/v1/routecategories/create" \
  -H "Content-Type: application/json" \
  -d '{
    "routeCategories": [
      {
        "groupId": "new-group-id",
        "languageId": "english-id",
        "status": 0,
        "title": "Adventure Tours",
        "description": "Exciting adventure tour packages",
        "icon": "fas fa-mountain"
      }
    ]
  }'
```

### Search Categories
```bash
curl -X POST "https://api.example.com/api/v1/routecategories" \
  -H "Content-Type: application/json" \
  -d '{
    "searchTerm": "adventure",
    "status": 1
  }'
```

---

## Swagger/OpenAPI Integration

This API is fully documented in Swagger/OpenAPI format with:
- Interactive examples for all endpoints
- Request/response schema definitions
- Parameter descriptions and validation rules
- Error response examples
- Authentication requirements

Access the interactive documentation at: `/swagger/index.html`
