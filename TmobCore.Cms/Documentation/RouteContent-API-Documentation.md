# RouteContent API Documentation

## Overview
The RouteContent API provides complete CRUD operations for managing route content associated with route categories in the homepage "Popular Routes" section. It supports multi-language content management, SEO optimization, and comprehensive content filtering.

## Base URL
```
/api/v1/routecontents
```

## Authentication
All endpoints require proper authentication headers.

## Endpoints

### 1. GET Route Contents
**Endpoint:** `POST /api/v1/routecontents`

**Description:** Retrieve route contents with optional filtering by groupId, languageId, categoryId, status, and search term.

**Method:** POST  
**Content-Type:** application/json

#### Request Body
```json
{
  "groupId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa7",
  "categoryId": "3fa85f64-5717-4562-b3fc-2c963f66afa8",
  "status": 1,
  "searchTerm": "travel guide",
  "page": 1,
  "pageSize": 10
}
```

#### Request Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| groupId | UUID | No | Filter by group ID for multilingual content |
| languageId | UUID | No | Filter by specific language |
| categoryId | UUID | No | Filter by route category |
| status | Integer | No | Filter by status (0=Draft, 1=Active, 2=Passive) |
| searchTerm | String | No | Search across title, description, content, meta fields |
| page | Integer | No | Page number (default: 1) |
| pageSize | Integer | No | Items per page (default: 10000) |

#### Response (200 OK)
```json
{
  "data": [
    {
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa9",
      "projectId": "3fa85f64-5717-4562-b3fc-2c963f66afa1",
      "userId": "3fa85f64-5717-4562-b3fc-2c963f66afa2",
      "groupId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa7",
      "status": 1,
      "title": "Complete Travel Guide to Istanbul",
      "description": "Discover the best attractions, restaurants, and hidden gems in Istanbul",
      "directUrl": "https://example.com/istanbul-guide",
      "metaTitle": "Istanbul Travel Guide 2025 | Best Places to Visit",
      "metaDescription": "Complete guide to Istanbul with top attractions, local cuisine, and travel tips for 2025",
      "keywords": "istanbul, travel, guide, turkey, attractions, tourism",
      "content": "<h1>Welcome to Istanbul</h1><p>Istanbul is a magnificent city...</p>",
      "categoryId": "3fa85f64-5717-4562-b3fc-2c963f66afa8",
      "deleted": false,
      "createdAt": "2025-07-05T10:30:00Z",
      "updatedAt": "2025-07-05T10:30:00Z"
    }
  ],
  "isSuccessful": true,
  "message": null,
  "errors": null,
  "page": 1,
  "pageSize": 10,
  "totalCount": 1,
  "totalPages": 1,
  "hasNextPage": false,
  "hasPreviousPage": false
}
```

---

### 2. GET Route Content by ID
**Endpoint:** `GET /api/v1/routecontents/{id}`

**Description:** Retrieve a specific route content by its unique identifier.

**Method:** GET

#### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | UUID | Yes | The unique identifier of the route content |

#### Response (200 OK)
```json
{
  "data": {
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa9",
    "projectId": "3fa85f64-5717-4562-b3fc-2c963f66afa1",
    "userId": "3fa85f64-5717-4562-b3fc-2c963f66afa2",
    "groupId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa7",
    "status": 1,
    "title": "Complete Travel Guide to Istanbul",
    "description": "Discover the best attractions, restaurants, and hidden gems in Istanbul",
    "directUrl": "https://example.com/istanbul-guide",
    "metaTitle": "Istanbul Travel Guide 2025 | Best Places to Visit",
    "metaDescription": "Complete guide to Istanbul with top attractions, local cuisine, and travel tips for 2025",
    "keywords": "istanbul, travel, guide, turkey, attractions, tourism",
    "content": "<h1>Welcome to Istanbul</h1><p>Istanbul is a magnificent city that bridges Europe and Asia...</p>",
    "categoryId": "3fa85f64-5717-4562-b3fc-2c963f66afa8",
    "deleted": false,
    "createdAt": "2025-07-05T10:30:00Z",
    "updatedAt": "2025-07-05T10:30:00Z"
  },
  "isSuccessful": true,
  "message": null,
  "errors": null
}
```

#### Response (404 Not Found)
```json
{
  "data": null,
  "isSuccessful": false,
  "message": "RouteContent not found for this id 3fa85f64-5717-4562-b3fc-2c963f66afa9",
  "errors": null
}
```

---

### 3. CREATE Route Contents
**Endpoint:** `POST /api/v1/routecontents/create`

**Description:** Create one or more route contents. Multiple language versions can be created by providing different languageId values with the same groupId.

**Method:** POST  
**Content-Type:** application/json

#### Request Body
```json
{
  "routeContents": [
    {
      "status": 0,
      "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa7",
      "groupId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "title": "Complete Travel Guide to Istanbul",
      "description": "Discover the best attractions, restaurants, and hidden gems in Istanbul",
      "directUrl": "https://example.com/istanbul-guide",
      "metaTitle": "Istanbul Travel Guide 2025 | Best Places to Visit",
      "metaDescription": "Complete guide to Istanbul with top attractions, local cuisine, and travel tips for 2025",
      "keywords": "istanbul, travel, guide, turkey, attractions, tourism",
      "content": "<h1>Welcome to Istanbul</h1><p>Istanbul is a magnificent city that bridges Europe and Asia. This comprehensive guide will help you discover the best attractions, local cuisine, and hidden gems that make Istanbul one of the world's most captivating destinations.</p><h2>Top Attractions</h2><ul><li>Hagia Sophia</li><li>Blue Mosque</li><li>Topkapi Palace</li><li>Grand Bazaar</li></ul>",
      "categoryId": "3fa85f64-5717-4562-b3fc-2c963f66afa8"
    },
    {
      "status": 0,
      "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa5",
      "groupId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "title": "İstanbul Kapsamlı Seyahat Rehberi",
      "description": "İstanbul'un en iyi mekanlarını, restoranlarını ve gizli köşelerini keşfedin",
      "directUrl": "https://example.com/istanbul-rehberi",
      "metaTitle": "İstanbul Seyahat Rehberi 2025 | Gezilecek En İyi Yerler",
      "metaDescription": "2025 için en iyi mekanlar, yerel mutfak ve seyahat ipuçları ile kapsamlı İstanbul rehberi",
      "keywords": "istanbul, seyahat, rehber, türkiye, mekanlar, turizm",
      "content": "<h1>İstanbul'a Hoş Geldiniz</h1><p>İstanbul, Avrupa ve Asya'yı birbirine bağlayan muhteşem bir şehirdir...</p>",
      "categoryId": "3fa85f64-5717-4562-b3fc-2c963f66afa8"
    }
  ]
}
```

#### Request Body Schema
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| routeContents | Array | Yes | Array of route content objects to create |
| routeContents[].status | Integer | Yes | Status (0=Draft, 1=Active, 2=Passive) |
| routeContents[].languageId | UUID | Yes | Language identifier |
| routeContents[].groupId | UUID | Yes | Group identifier for multilingual content |
| routeContents[].title | String | Yes | Content title (max 255 characters) |
| routeContents[].description | String | Yes | Content description |
| routeContents[].directUrl | String | No | Direct URL to the content |
| routeContents[].metaTitle | String | No | SEO meta title |
| routeContents[].metaDescription | String | No | SEO meta description |
| routeContents[].keywords | String | No | SEO keywords (comma-separated) |
| routeContents[].content | String | Yes | HTML content body |
| routeContents[].categoryId | UUID | Yes | Associated route category ID |

#### Response (200 OK)
```json
{
  "data": true,
  "isSuccessful": true,
  "message": null,
  "errors": null
}
```

#### Response (400 Bad Request)
```json
{
  "data": false,
  "isSuccessful": false,
  "message": "Title is required, Content is required, CategoryId is required",
  "errors": [
    "Title is required for route content at index 0",
    "Content cannot be empty for route content at index 1",
    "CategoryId must be a valid UUID"
  ]
}
```

---

### 4. UPDATE Route Contents
**Endpoint:** `PUT /api/v1/routecontents`

**Description:** Update existing route contents.

**Method:** PUT  
**Content-Type:** application/json

#### Request Body
```json
{
  "routeContents": [
    {
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa9",
      "status": 1,
      "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa7",
      "groupId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "title": "Ultimate Travel Guide to Istanbul 2025",
      "description": "The most comprehensive guide to Istanbul with updated information for 2025",
      "directUrl": "https://example.com/istanbul-ultimate-guide",
      "metaTitle": "Ultimate Istanbul Travel Guide 2025 | Complete City Guide",
      "metaDescription": "The ultimate guide to Istanbul with the latest attractions, restaurants, and insider tips for 2025",
      "keywords": "istanbul, ultimate guide, travel, 2025, turkey, attractions, tourism, complete guide",
      "content": "<h1>Ultimate Istanbul Experience</h1><p>Updated for 2025, this is your complete guide to experiencing Istanbul like a local...</p>",
      "categoryId": "3fa85f64-5717-4562-b3fc-2c963f66afa8"
    }
  ]
}
```

#### Request Body Schema
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| routeContents | Array | Yes | Array of route content objects to update |
| routeContents[].id | UUID | Yes | Unique identifier of the content to update |
| routeContents[].status | Integer | Yes | Status (0=Draft, 1=Active, 2=Passive) |
| routeContents[].languageId | UUID | Yes | Language identifier |
| routeContents[].groupId | UUID | Yes | Group identifier for multilingual content |
| routeContents[].title | String | Yes | Content title (max 255 characters) |
| routeContents[].description | String | Yes | Content description |
| routeContents[].directUrl | String | No | Direct URL to the content |
| routeContents[].metaTitle | String | No | SEO meta title |
| routeContents[].metaDescription | String | No | SEO meta description |
| routeContents[].keywords | String | No | SEO keywords (comma-separated) |
| routeContents[].content | String | Yes | HTML content body |
| routeContents[].categoryId | UUID | Yes | Associated route category ID |

#### Response (200 OK)
```json
{
  "data": true,
  "isSuccessful": true,
  "message": null,
  "errors": null
}
```

#### Response (400 Bad Request)
```json
{
  "data": false,
  "isSuccessful": false,
  "message": "RouteContent not found for id: 3fa85f64-5717-4562-b3fc-2c963f66afa9",
  "errors": null
}
```

---

### 5. DELETE Route Contents
**Endpoint:** `DELETE /api/v1/routecontents`

**Description:** Soft delete route contents by their IDs. Returns the count of successfully deleted items.

**Method:** DELETE  
**Content-Type:** application/json

#### Request Body
```json
{
  "ids": [
    "3fa85f64-5717-4562-b3fc-2c963f66afa9",
    "3fa85f64-5717-4562-b3fc-2c963f66afab"
  ]
}
```

#### Request Body Schema
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| ids | Array[UUID] | Yes | Array of route content IDs to delete |

#### Response (200 OK)
```json
{
  "data": 2,
  "isSuccessful": true,
  "message": null,
  "errors": null
}
```

#### Response (400 Bad Request - Invalid IDs)
```json
{
  "data": 0,
  "isSuccessful": false,
  "message": "Invalid RouteContent IDs: 3fa85f64-5717-4562-b3fc-2c963f66afa9",
  "errors": null
}
```

#### Response (404 Not Found)
```json
{
  "data": 0,
  "isSuccessful": false,
  "message": "No route contents found to delete",
  "errors": null
}
```

---

## Data Models

### RouteContent Response Model
```json
{
  "id": "UUID",
  "projectId": "UUID",
  "userId": "UUID",
  "groupId": "UUID", 
  "languageId": "UUID",
  "status": "Integer (0=Draft, 1=Active, 2=Passive)",
  "title": "String",
  "description": "String",
  "directUrl": "String",
  "metaTitle": "String",
  "metaDescription": "String", 
  "keywords": "String",
  "content": "String (HTML)",
  "categoryId": "UUID",
  "deleted": "Boolean",
  "createdAt": "DateTime (ISO 8601)",
  "updatedAt": "DateTime (ISO 8601)"
}
```

### Status Values
| Value | Description |
|-------|-------------|
| 0 | Draft |
| 1 | Active |
| 2 | Passive |

---

## SEO Features

### Meta Fields
The RouteContent API includes comprehensive SEO support:

- **metaTitle**: SEO-optimized page title
- **metaDescription**: Meta description for search engines
- **keywords**: Comma-separated keywords for SEO
- **directUrl**: Canonical URL for the content

### SEO Best Practices
```json
{
  "title": "Istanbul Travel Guide",
  "metaTitle": "Istanbul Travel Guide 2025 | Best Places to Visit",
  "metaDescription": "Complete guide to Istanbul with top attractions, local cuisine, and travel tips for 2025. Discover the best of Turkey's cultural capital.",
  "keywords": "istanbul, travel guide, turkey tourism, attractions, restaurants, hotels, things to do",
  "directUrl": "https://example.com/travel-guides/istanbul"
}
```

---

## Multi-Language Support

### Grouping Content by Language
- Use the same `groupId` for different language versions of the same route content
- Provide different `languageId` values for each language
- Filter by `groupId` to get all language versions
- Filter by `languageId` to get specific language version

### Example Multi-Language Creation
```json
{
  "routeContents": [
    {
      "groupId": "same-group-id-for-all-languages",
      "languageId": "english-language-id",
      "title": "Istanbul Travel Guide",
      "description": "Complete guide to Istanbul",
      "content": "<h1>Welcome to Istanbul</h1>...",
      "metaTitle": "Istanbul Travel Guide 2025",
      "keywords": "istanbul, travel, guide, turkey"
    },
    {
      "groupId": "same-group-id-for-all-languages",
      "languageId": "turkish-language-id", 
      "title": "İstanbul Seyahat Rehberi",
      "description": "İstanbul için kapsamlı rehber",
      "content": "<h1>İstanbul'a Hoş Geldiniz</h1>...",
      "metaTitle": "İstanbul Seyahat Rehberi 2025",
      "keywords": "istanbul, seyahat, rehber, türkiye"
    }
  ]
}
```

---

## Search Functionality

The search feature searches across multiple fields:
- **title**: Content title
- **description**: Content description  
- **content**: HTML content body
- **metaTitle**: SEO meta title
- **metaDescription**: SEO meta description
- **keywords**: SEO keywords

### Search Example
```json
{
  "searchTerm": "istanbul attractions",
  "status": 1,
  "languageId": "english-language-id"
}
```

---

## Content Filtering

### Filter by Category
```json
{
  "categoryId": "3fa85f64-5717-4562-b3fc-2c963f66afa8",
  "status": 1
}
```

### Filter by Language and Group
```json
{
  "groupId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa7"
}
```

### Complex Filtering
```json
{
  "categoryId": "3fa85f64-5717-4562-b3fc-2c963f66afa8",
  "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa7",
  "status": 1,
  "searchTerm": "travel guide",
  "page": 1,
  "pageSize": 20
}
```

---

## Error Handling

All endpoints return consistent error responses:

```json
{
  "data": null,
  "isSuccessful": false,
  "message": "Error description",
  "errors": ["Detailed error messages"]
}
```

### Common HTTP Status Codes
- **200**: Success
- **400**: Bad Request (validation errors, invalid parameters)
- **404**: Not Found (resource doesn't exist)
- **500**: Internal Server Error

---

## Usage Examples

### Get All Active Content for a Category
```bash
curl -X POST "https://api.example.com/api/v1/routecontents" \
  -H "Content-Type: application/json" \
  -d '{
    "categoryId": "category-uuid",
    "status": 1,
    "languageId": "english-language-id"
  }'
```

### Create SEO-Optimized Content
```bash
curl -X POST "https://api.example.com/api/v1/routecontents/create" \
  -H "Content-Type: application/json" \
  -d '{
    "routeContents": [
      {
        "groupId": "new-group-id",
        "languageId": "english-id",
        "categoryId": "category-id",
        "status": 0,
        "title": "Best Restaurants in Istanbul",
        "description": "Discover the finest dining experiences in Istanbul",
        "metaTitle": "Best Restaurants in Istanbul 2025 | Food Guide",
        "metaDescription": "Discover the best restaurants in Istanbul with our comprehensive food guide featuring local cuisine and fine dining options.",
        "keywords": "istanbul restaurants, turkish cuisine, fine dining, food guide",
        "content": "<h1>Best Restaurants in Istanbul</h1><p>Istanbul offers incredible dining...</p>",
        "directUrl": "https://example.com/istanbul-restaurants"
      }
    ]
  }'
```

### Search Content
```bash
curl -X POST "https://api.example.com/api/v1/routecontents" \
  -H "Content-Type: application/json" \
  -d '{
    "searchTerm": "restaurant guide",
    "status": 1,
    "languageId": "english-id"
  }'
```

---

## Swagger/OpenAPI Integration

This API is fully documented in Swagger/OpenAPI format with:
- Interactive examples for all endpoints
- Request/response schema definitions
- Parameter descriptions and validation rules
- Error response examples
- Authentication requirements
- SEO field documentation

Access the interactive documentation at: `/swagger/index.html`
