# RouteCategory API Documentation

## Overview
The RouteCategory API provides CRUD operations for managing route categories used in the homepage "Popular Routes" section. All endpoints support multi-language content through `groupId` and `languageId` parameters.

## Base URL
```
/api/v1/routecategories
```

## Endpoints

### 1. GET Route Categories (POST /api/v1/routecategories)

**Description:** Retrieve route categories with optional filtering by groupId, languageId, status, and search term.

**Request Body:**
```json
{
  "groupId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa7",
  "status": 1,
  "searchTerm": "popular",
  "page": 1,
  "pageSize": 10
}
```

**Response (200 OK):**
```json
{
  "data": [
    {
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "projectId": "3fa85f64-5717-4562-b3fc-2c963f66afa1",
      "userId": "3fa85f64-5717-4562-b3fc-2c963f66afa2",
      "groupId": "3fa85f64-5717-4562-b3fc-2c963f66afa3",
      "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa4",
      "status": 1,
      "title": "Popular Destinations",
      "description": "Discover the most popular travel destinations",
      "icon": "fas fa-plane",
      "deleted": false,
      "createdAt": "2025-07-05T10:30:00Z",
      "updatedAt": "2025-07-05T10:30:00Z"
    }
  ],
  "isSuccessful": true,
  "message": null,
  "errors": null,
  "page": 1,
  "pageSize": 10,
  "totalCount": 1,
  "totalPages": 1,
  "hasNextPage": false,
  "hasPreviousPage": false
}
```

### 2. GET Route Category by ID (GET /api/v1/routecategories/{id})

**Description:** Retrieve a specific route category by its ID.

**Parameters:**
- `id` (path): The unique identifier of the route category

**Response (200 OK):**
```json
{
  "data": {
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "projectId": "3fa85f64-5717-4562-b3fc-2c963f66afa1",
    "userId": "3fa85f64-5717-4562-b3fc-2c963f66afa2",
    "groupId": "3fa85f64-5717-4562-b3fc-2c963f66afa3",
    "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa4",
    "status": 1,
    "title": "Popular Destinations",
    "description": "Discover the most popular travel destinations",
    "icon": "fas fa-plane",
    "deleted": false,
    "createdAt": "2025-07-05T10:30:00Z",
    "updatedAt": "2025-07-05T10:30:00Z"
  },
  "isSuccessful": true,
  "message": null,
  "errors": null
}
```

**Response (404 Not Found):**
```json
{
  "data": null,
  "isSuccessful": false,
  "message": "RouteCategory not found for this id 3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "errors": null
}
```

### 3. CREATE Route Categories (POST /api/v1/routecategories/create)

**Description:** Create one or more route categories. Multiple language versions can be created by providing different languageId values with the same groupId.

**Request Body:**
```json
{
  "routeCategories": [
    {
      "status": 0,
      "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa4",
      "groupId": "3fa85f64-5717-4562-b3fc-2c963f66afa3",
      "title": "Popular Destinations",
      "description": "Discover the most popular travel destinations around the world",
      "icon": "fas fa-plane"
    },
    {
      "status": 0,
      "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa5",
      "groupId": "3fa85f64-5717-4562-b3fc-2c963f66afa3",
      "title": "Popüler Destinasyonlar",
      "description": "Dünyanın en popüler seyahat destinasyonlarını keşfedin",
      "icon": "fas fa-plane"
    }
  ]
}
```

**Response (200 OK):**
```json
{
  "data": true,
  "isSuccessful": true,
  "message": null,
  "errors": null
}
```

**Response (400 Bad Request):**
```json
{
  "data": false,
  "isSuccessful": false,
  "message": "Title is required, Description is required",
  "errors": null
}
```

### 4. UPDATE Route Categories (PUT /api/v1/routecategories)

**Description:** Update existing route categories.

**Request Body:**
```json
{
  "routeCategories": [
    {
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "status": 1,
      "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa4",
      "groupId": "3fa85f64-5717-4562-b3fc-2c963f66afa3",
      "title": "Most Popular Destinations",
      "description": "Discover the most popular travel destinations worldwide",
      "icon": "fas fa-globe"
    }
  ]
}
```

**Response (200 OK):**
```json
{
  "data": true,
  "isSuccessful": true,
  "message": null,
  "errors": null
}
```

### 5. DELETE Route Categories (DELETE /api/v1/routecategories)

**Description:** Soft delete route categories by their IDs. Returns the count of successfully deleted items.

**Request Body:**
```json
{
  "ids": [
    "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "3fa85f64-5717-4562-b3fc-2c963f66afa7"
  ]
}
```

**Response (200 OK):**
```json
{
  "data": 2,
  "isSuccessful": true,
  "message": null,
  "errors": null
}
```

**Response (400 Bad Request - Invalid IDs):**
```json
{
  "data": 0,
  "isSuccessful": false,
  "message": "Invalid RouteCategory IDs: 3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "errors": null
}
```

## Status Values
- `0`: Draft
- `1`: Active  
- `2`: Passive

## Multi-Language Support
- Use the same `groupId` for different language versions of the same route category
- Provide different `languageId` values for each language
- Filter by `groupId` to get all language versions
- Filter by `languageId` to get specific language version

## Error Handling
All endpoints return consistent error responses with:
- `isSuccessful`: false
- `message`: Error description
- `errors`: Additional error details (if any)
- `data`: null or default value

## Validation Rules
- **Title**: Required, max 255 characters
- **Description**: Required, max 1000 characters  
- **Icon**: Required, max 255 characters
- **LanguageId**: Required for create/update operations
- **GroupId**: Optional for create (auto-generated if not provided)
