using AutoMapper;
using TmobCore.Cms.Application.Features.Country.Commands.CreateCountry;
using TmobCore.Cms.Application.Features.Country.Commands.UpdateCountry;
using TmobCore.Cms.Application.Models.Country;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Application.MappingProfiles
{
    public class CountryProfile : Profile
    {
        public CountryProfile()
        {
            CreateMap<CreateCountryRequest, Country>()
                .ForMember(dest => dest.DateCreated, opt => opt.MapFrom(src => DateTime.UtcNow))
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.ProjectId, opt => opt.Ignore())
                .ForMember(dest => dest.UserId, opt => opt.Ignore());
            CreateMap<UpdateCountryRequest, Country>()
                .ForMember(dest => dest.DateModified, opt => opt.MapFrom(src => DateTime.UtcNow))
                .ForMember(dest => dest.ProjectId, opt => opt.Ignore())
                .ForMember(dest => dest.UserId, opt => opt.Ignore());
            CreateMap<Country, CountryResponse>()
                .ForMember(dest => dest.File, opt => opt.MapFrom(src => src.Image))
                .ReverseMap();
        }
    }
}
