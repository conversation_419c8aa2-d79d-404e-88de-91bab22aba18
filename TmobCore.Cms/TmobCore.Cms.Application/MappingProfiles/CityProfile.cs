using AutoMapper;
using TmobCore.Cms.Application.Features.City.Commands.CreateCity;
using TmobCore.Cms.Application.Features.City.Commands.UpdateCity;
using TmobCore.Cms.Application.Models.City;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Application.MappingProfiles
{
    public class CityProfile : Profile
    {
        public CityProfile()
        {
            CreateMap<CreateCityRequest, City>()
                .ForMember(dest => dest.DateCreated, opt => opt.MapFrom(src => DateTime.UtcNow))
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.ProjectId, opt => opt.Ignore())
                .ForMember(dest => dest.UserId, opt => opt.Ignore());
            CreateMap<UpdateCityRequest, City>()
                .ForMember(dest => dest.DateCreated, opt => opt.MapFrom(src => DateTime.UtcNow))
                .ForMember(dest => dest.ProjectId, opt => opt.Ignore())
                .ForMember(dest => dest.UserId, opt => opt.Ignore());
            CreateMap<City, CityResponse>()
                .ForMember(dest => dest.Country, opt => opt.MapFrom(src => src.Country))
                .ForMember(dest => dest.File, opt => opt.MapFrom(src => src.Image));

        }
    }
}
