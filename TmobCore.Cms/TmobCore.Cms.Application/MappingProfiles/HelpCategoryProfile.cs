using AutoMapper;
using TmobCore.Cms.Application.Features.HelpCategory.Commands.Create;
using TmobCore.Cms.Application.Features.HelpCategory.Commands.Update;
using TmobCore.Cms.Application.Models.HelpCategory;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Application.MappingProfiles
{
    public class HelpCategoryProfile : Profile
    {
        public HelpCategoryProfile()
        {
            // Controller → Command dönüşümü
            CreateMap<CreateHelpCategoryRequest, CreateHelpCategoryCommand>();
            CreateMap<TmobCore.Cms.Application.Models.HelpCategory.CreateHelpCategoryRequest, CreateHelpCategoryCommandModel>();

            // Application → Entity dönüşümleri
            CreateMap<CreateHelpCategoryCommand, HelpCategory>();
            CreateMap<CreateHelpCategoryCommandModel, HelpCategory>();

            // Entity → Response
            CreateMap<HelpCategory, HelpCategoryResponse>()
                .ForMember(dest => dest.ParentCategoryTitle, opt =>
                    opt.MapFrom(src => src.ParentCategory != null ? src.ParentCategory.Title : null));

            // Update işlemleri
            CreateMap<UpdateHelpCategoryRequest, UpdateHelpCategoryCommand>();
            CreateMap<UpdateHelpCategoryCommand, HelpCategory>();
        }
    }
}