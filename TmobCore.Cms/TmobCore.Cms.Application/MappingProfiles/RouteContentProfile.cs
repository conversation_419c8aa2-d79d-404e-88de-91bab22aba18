using AutoMapper;
using TmobCore.Cms.Application.Features.RouteContent.Commands.CreateRouteContent;
using TmobCore.Cms.Application.Features.RouteContent.Commands.UpdateRouteContent;
using TmobCore.Cms.Application.Models.RouteContent;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Application.MappingProfiles
{
    public class RouteContentProfile : Profile
    {
        public RouteContentProfile()
        {
            CreateMap<CreateRouteContentRequest, RouteContent>().ReverseMap();
            CreateMap<UpdateRouteContentRequest, RouteContent>().ReverseMap();
            CreateMap<RouteContent, RouteContentResponse>()
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.DateCreated))
                .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => src.DateModified))
                .ReverseMap();
        }
    }
}
