using TmobCore.Cms.Domain.Enums;

namespace TmobCore.Cms.Application.Models.RouteContent
{
    public class RouteContentRequest
    {
        public Guid? GroupId { get; set; }
        public Guid? LanguageId { get; set; }
        public Guid? ProjectId { get; set; }
        public Guid? CategoryId { get; set; }
        public RouteCategoryStatus? Status { get; set; }
        public string? SearchTerm { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10000;
    }
}
