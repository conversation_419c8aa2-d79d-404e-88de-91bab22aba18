using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Domain.Enums;

namespace TmobCore.Cms.Application.Models.RouteContent
{
    public class RouteContentResponse : BaseResponse
    {
        public Guid Id { get; set; }
        public Guid ProjectId { get; set; }
        public Guid UserId { get; set; }
        public Guid GroupId { get; set; }
        public Guid LanguageId { get; set; }
        public RouteCategoryStatus Status { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string DirectUrl { get; set; } = string.Empty;
        public string MetaTitle { get; set; } = string.Empty;
        public string MetaDescription { get; set; } = string.Empty;
        public string Keywords { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public Guid CategoryId { get; set; }
        public bool Deleted { get; set; }
    }
}
