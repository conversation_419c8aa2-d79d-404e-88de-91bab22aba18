using TmobCore.Cms.Application.Models.RouteContent;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Application.Contracts.Persistence
{
    public interface IRouteContentRepository : IGenericRepository<RouteContent>
    {
        Task<(List<RouteContent> RouteContents, int TotalCount)> GetRouteContents(RouteContentRequest request, CancellationToken cancellationToken);
        Task<RouteContent> GetRouteContentByIdAsync(Guid? routeContentId, CancellationToken cancellationToken);
        Task<List<RouteContent>> GetRouteContentByGroupIdAsync(Guid groupId);
        Task<List<RouteContent>> GetRouteContentByCategoryIdAsync(Guid categoryId);
    }
}
