using MediatR;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Domain.Enums;

namespace TmobCore.Cms.Application.Features.SpecialOfferWidget.Commands.Create
{
    public class CreateSpecialOfferWidgetCommand : IRequest<ActionResponse<Guid>>
    {
        public string Origin { get; set; } = null!;
        public int Priority { get; set; } = 0;
        public Tag Tag { get; set; } = Tag.None;
        public DateTime FlightStartDate { get; set; }
        public DateTime FlightEndDate { get; set; }
        public DateTime PublishedStartDate { get; set; }
        public DateTime PublishedEndDate { get; set; }

        public List<PriceInCurrencyRequest> Prices { get; set; } = new();
    }
}