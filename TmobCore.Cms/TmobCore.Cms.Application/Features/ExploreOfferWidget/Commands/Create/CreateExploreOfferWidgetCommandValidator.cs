using FluentValidation;

namespace TmobCore.Cms.Application.Features.ExploreOfferWidget.Commands.Create
{
    public class CreateExploreOfferWidgetCommandValidator : AbstractValidator<CreateExploreOfferWidgetCommand>
    {
        public CreateExploreOfferWidgetCommandValidator()
        {
            RuleFor(x => x.Title).NotEmpty();

            RuleFor(x => x.FlightEndDate)
                .GreaterThanOrEqualTo(x => x.FlightStartDate);

            RuleFor(x => x.PublishedEndDate)
                .GreaterThanOrEqualTo(x => x.PublishedStartDate);

            RuleForEach(x => x.Routes).ChildRules(route =>
            {
                route.RuleFor(r => r.Destination).NotEmpty();
                route.RuleForEach(r => r.Prices).ChildRules(price =>
                {
                    price.RuleFor(p => p.Currency).NotEmpty();
                    price.RuleFor(p => p.MaxPrice).GreaterThan(0);
                });
            });
        }
    }
}