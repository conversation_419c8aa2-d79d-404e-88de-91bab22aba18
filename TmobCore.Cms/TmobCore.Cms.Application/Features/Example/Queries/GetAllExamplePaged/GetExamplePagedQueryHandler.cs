using AutoMapper;
using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Features.Example.Queries.GetAllExamplePaged;
using TmobCore.Cms.Application.Features.Example.Queries.GetAllExamples;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Domain.Entities;

public class GetExamplePagedQueryHandler : IRequestHandler<GetExamplePagedQuery, ActionResponse<ExampleDto>>
{
    private readonly IMapper _mapper;
    private readonly IExampleRepository _exampleRepository;
    private readonly IAppLogger<GetExamplePagedQueryHandler> _logger;

    public GetExamplePagedQueryHandler(IMapper mapper, IExampleRepository exampleRepository, IAppLogger<GetExamplePagedQueryHandler> logger)
    {
        _mapper = mapper;
        _exampleRepository = exampleRepository;
        _logger = logger;
    }

    public async Task<ActionResponse<ExampleDto>> Handle(GetExamplePagedQuery request, CancellationToken cancellationToken)
    {
        //Query the database
        var example = _exampleRepository.GetQuery();

        //gets pagination data 
        var pagedData = await PagedActionResponse<Example>.CreateAsync(example, request.Page, request.PageSize);

        //convert data objects to DTO objects
        var data = _mapper.Map<ActionResponse<ExampleDto>>(pagedData);

        //sets information log
        _logger.LogInformation("Example were retrieved successfully.");

        return data;

    }
}
