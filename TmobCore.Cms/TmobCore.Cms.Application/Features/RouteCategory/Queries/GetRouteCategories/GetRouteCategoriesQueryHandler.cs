using AutoMapper;
using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Contracts.User;
using TmobCore.Cms.Application.Helper;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.RouteCategory;

namespace TmobCore.Cms.Application.Features.RouteCategory.Queries.GetRouteCategories
{
    public class GetRouteCategoriesQueryHandler : IRequestHandler<GetRouteCategoriesQuery, ActionResponse<List<RouteCategoryResponse>>>
    {
        private readonly IRouteCategoryRepository _routeCategoryRepository;
        private readonly IUserPrincipal _userPrincipal;
        private readonly IMapper _mapper;
        private readonly IAppLogger<GetRouteCategoriesQueryHandler> _logger;

        public GetRouteCategoriesQueryHandler(
            IRouteCategoryRepository routeCategoryRepository,
            IUserPrincipal userPrincipal,
            IMapper mapper,
            IAppLogger<GetRouteCategoriesQueryHandler> logger)
        {
            _routeCategoryRepository = routeCategoryRepository;
            _userPrincipal = userPrincipal;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<ActionResponse<List<RouteCategoryResponse>>> Handle(GetRouteCategoriesQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var routeCategoriesRequest = new RouteCategoryRequest
                {
                    ProjectId = _userPrincipal.ProjectId.ToGuid(),
                    SearchTerm = request.SearchTerm,
                    Page = request.Page,
                    PageSize = request.PageSize,
                    GroupId = request.GroupId,
                    LanguageId = request.LanguageId,
                    Status = request.Status
                };

                var (routeCategories, totalCount) = await _routeCategoryRepository.GetRouteCategories(routeCategoriesRequest, cancellationToken);
                var routeCategoriesResponse = _mapper.Map<List<RouteCategoryResponse>>(routeCategories);

                _logger.LogInformation($"Retrieved {routeCategories.Count} RouteCategories successfully");

                return ActionResponse<List<RouteCategoryResponse>>.Success(routeCategoriesResponse, StatusCode.Ok)
                    .WithPagination(request.Page, request.PageSize, totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Failed to retrieve RouteCategories: {ex}");
                return ActionResponse<List<RouteCategoryResponse>>.Fail(ex.ToString(), StatusCode.BadRequest);
            }
        }
    }
}
