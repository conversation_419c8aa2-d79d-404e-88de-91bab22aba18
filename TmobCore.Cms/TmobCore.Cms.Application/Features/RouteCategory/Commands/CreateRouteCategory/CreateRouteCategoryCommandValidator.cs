using FluentValidation;

namespace TmobCore.Cms.Application.Features.RouteCategory.Commands.CreateRouteCategory
{
    public class CreateRouteCategoryCommandValidator : AbstractValidator<CreateRouteCategoryCommand>
    {
        public CreateRouteCategoryCommandValidator()
        {
            RuleFor(x => x.RouteCategories)
                .NotEmpty()
                .WithMessage("At least one route category is required");

            RuleForEach(p => p.RouteCategories).ChildRules(routeCategory =>
            {
                routeCategory.RuleFor(rc => rc.Title)
                    .NotEmpty()
                    .WithMessage("{PropertyName} is required")
                    .MaximumLength(255)
                    .WithMessage("{PropertyName} must not exceed 255 characters");

                routeCategory.RuleFor(rc => rc.Description)
                    .NotEmpty()
                    .WithMessage("{PropertyName} is required")
                    .MaximumLength(1000)
                    .WithMessage("{PropertyName} must not exceed 1000 characters");

                routeCategory.RuleFor(rc => rc.Icon)
                    .NotEmpty()
                    .WithMessage("{PropertyName} is required")
                    .MaximumLength(255)
                    .WithMessage("{PropertyName} must not exceed 255 characters");

                routeCategory.RuleFor(rc => rc.LanguageId)
                    .NotEmpty()
                    .WithMessage("{PropertyName} is required");
            });
        }
    }
}
