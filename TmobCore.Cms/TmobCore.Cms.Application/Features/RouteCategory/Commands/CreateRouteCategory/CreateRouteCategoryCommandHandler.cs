using AutoMapper;
using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Contracts.User;
using TmobCore.Cms.Application.Exceptions;
using TmobCore.Cms.Application.Helper;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.RouteCategory.Commands.CreateRouteCategory
{
    public class CreateRouteCategoryCommandHandler : IRequestHandler<CreateRouteCategoryCommand, ActionResponse<bool>>
    {
        private readonly IRouteCategoryRepository _routeCategoryRepository;
        private readonly IUserPrincipal _userPrincipal;
        private readonly IMapper _mapper;
        private readonly IAppLogger<CreateRouteCategoryCommandHandler> _logger;
        private readonly IUnitOfWork _uow;

        public CreateRouteCategoryCommandHandler(
            IRouteCategoryRepository routeCategoryRepository,
            IMapper mapper,
            IUserPrincipal userPrincipal,
            IAppLogger<CreateRouteCategoryCommandHandler> logger,
            IUnitOfWork uow)
        {
            _routeCategoryRepository = routeCategoryRepository;
            _mapper = mapper;
            _userPrincipal = userPrincipal;
            _logger = logger;
            _uow = uow;
        }

        public async Task<ActionResponse<bool>> Handle(CreateRouteCategoryCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var projectId = _userPrincipal.ProjectId.ToGuid()
                                ?? throw new NotFoundException("ProjectId", _userPrincipal.ProjectId);

                // Validation
                var validator = new CreateRouteCategoryCommandValidator();
                var validationResult = await validator.ValidateAsync(request);

                if (!validationResult.IsValid)
                {
                    var validationErrors = string.Join(", ", validationResult.Errors.Select(x => x.ErrorMessage));
                    _logger.LogWarning($"RouteCategory creation failed due to validation errors: {validationErrors}");
                    return ActionResponse<bool>.Fail(validationErrors, StatusCode.BadRequest);
                }

                var routeCategories = new List<Domain.Entities.RouteCategory>();
                var newGroupId = Guid.NewGuid();

                foreach (var routeCategoryRequest in request.RouteCategories)
                {
                    var routeCategory = _mapper.Map<Domain.Entities.RouteCategory>(routeCategoryRequest);
                    routeCategory.UserId = _userPrincipal.UserId;
                    routeCategory.ProjectId = projectId;
                    routeCategory.GroupId = routeCategoryRequest.GroupId ?? newGroupId;
                    routeCategory.LanguageId = routeCategoryRequest.LanguageId;

                    routeCategories.Add(routeCategory);
                }

                await _routeCategoryRepository.CreateRangeAsync(routeCategories);
                await _uow.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("RouteCategories created successfully");
                return ActionResponse<bool>.Success(true, StatusCode.Ok);
            }
            catch (NotFoundException notFoundException)
            {
                _logger.LogWarning($"RouteCategory creation failed due to exception errors {notFoundException}");
                return ActionResponse<bool>.Fail(notFoundException.ToString(), StatusCode.BadRequest);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"RouteCategory creation failed due to exception: {ex}");
                return ActionResponse<bool>.Fail(ex.ToString(), StatusCode.BadRequest);
            }
        }
    }
}
