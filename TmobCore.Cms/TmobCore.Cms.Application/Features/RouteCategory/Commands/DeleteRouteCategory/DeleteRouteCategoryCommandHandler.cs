using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.RouteCategory.Commands.DeleteRouteCategory
{
    public class DeleteRouteCategoryCommandHandler : IRequestHandler<DeleteRouteCategoryCommand, ActionResponse<int>>
    {
        private readonly IRouteCategoryRepository _routeCategoryRepository;
        private readonly IAppLogger<DeleteRouteCategoryCommandHandler> _logger;
        private readonly IUnitOfWork _uow;

        public DeleteRouteCategoryCommandHandler(
            IRouteCategoryRepository routeCategoryRepository,
            IAppLogger<DeleteRouteCategoryCommandHandler> logger,
            IUnitOfWork uow)
        {
            _routeCategoryRepository = routeCategoryRepository;
            _logger = logger;
            _uow = uow;
        }

        public async Task<ActionResponse<int>> Handle(DeleteRouteCategoryCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Validation
                var validator = new DeleteRouteCategoryCommandValidator();
                var validationResult = await validator.ValidateAsync(request, cancellationToken);

                if (!validationResult.IsValid)
                {
                    var validationErrors = string.Join(", ", validationResult.Errors.Select(x => x.ErrorMessage));
                    _logger.LogWarning($"RouteCategory deletion failed due to validation errors: {validationErrors}");
                    return ActionResponse<int>.Fail(validationErrors, StatusCode.BadRequest);
                }

                var deletedCount = 0;
                var invalidIds = new List<Guid>();

                foreach (var id in request.Ids)
                {
                    var routeCategory = await _routeCategoryRepository.GetByIdAsync(id);
                    if (routeCategory != null && !routeCategory.Deleted)
                    {
                        routeCategory.Deleted = true;
                        _routeCategoryRepository.Update(routeCategory);
                        deletedCount++;
                    }
                    else
                    {
                        invalidIds.Add(id);
                    }
                }

                if (invalidIds.Any())
                {
                    _logger.LogWarning($"Some RouteCategory IDs were invalid: {string.Join(", ", invalidIds)}");
                    return ActionResponse<int>.Fail($"Invalid RouteCategory IDs: {string.Join(", ", invalidIds)}", StatusCode.BadRequest);
                }

                if (deletedCount == 0)
                {
                    _logger.LogWarning("No RouteCategories found to delete", request.Ids);
                    return ActionResponse<int>.Fail("No RouteCategories found to delete", StatusCode.NotFound);
                }

                await _uow.SaveChangesAsync(cancellationToken);
                _logger.LogInformation($"{deletedCount} RouteCategories have been deleted successfully", request.Ids);

                return ActionResponse<int>.Success(deletedCount, StatusCode.Ok);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"RouteCategory deletion failed due to exception: {ex}");
                return ActionResponse<int>.Fail(ex.ToString(), StatusCode.BadRequest);
            }
        }
    }
}
