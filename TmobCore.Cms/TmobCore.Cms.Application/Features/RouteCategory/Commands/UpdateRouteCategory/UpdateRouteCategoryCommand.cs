using MediatR;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.RouteCategory.Commands.UpdateRouteCategory
{
    public class UpdateRouteCategoryCommand : IRequest<ActionResponse<bool>>
    {
        public List<UpdateRouteCategoryRequest> RouteCategories { get; set; } = new();
    }

    public class UpdateRouteCategoryRequest : BaseRequest
    {
        public Guid Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
    }
}
