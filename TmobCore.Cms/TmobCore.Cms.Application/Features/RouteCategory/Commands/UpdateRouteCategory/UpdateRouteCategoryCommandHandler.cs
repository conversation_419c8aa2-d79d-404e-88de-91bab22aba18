using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.RouteCategory.Commands.UpdateRouteCategory
{
    public class UpdateRouteCategoryCommandHandler : IRequestHandler<UpdateRouteCategoryCommand, ActionResponse<bool>>
    {
        private readonly IRouteCategoryRepository _routeCategoryRepository;
        private readonly IMapper _mapper;
        private readonly IAppLogger<UpdateRouteCategoryCommandHandler> _logger;
        private readonly IUnitOfWork _uow;

        public UpdateRouteCategoryCommandHandler(
            IRouteCategoryRepository routeCategoryRepository,
            IMapper mapper,
            IAppLogger<UpdateRouteCategoryCommandHandler> logger,
            IUnitOfWork uow)
        {
            _routeCategoryRepository = routeCategoryRepository;
            _mapper = mapper;
            _logger = logger;
            _uow = uow;
        }

        public async Task<ActionResponse<bool>> Handle(UpdateRouteCategoryCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Validation
                var validator = new UpdateRouteCategoryCommandValidator();
                var validationResult = await validator.ValidateAsync(request);

                if (!validationResult.IsValid)
                {
                    var validationErrors = string.Join(", ", validationResult.Errors.Select(x => x.ErrorMessage));
                    _logger.LogWarning($"RouteCategory update failed due to validation errors: {validationErrors}");
                    return ActionResponse<bool>.Fail(validationErrors, StatusCode.BadRequest);
                }

                foreach (var routeCategoryRequest in request.RouteCategories)
                {
                    var routeCategory = await _routeCategoryRepository
                        .GetQuery(x => x.Id == routeCategoryRequest.Id)
                        .FirstOrDefaultAsync(cancellationToken);

                    if (routeCategory == null)
                    {
                        _logger.LogWarning($"RouteCategory not found with id: {routeCategoryRequest.Id}");
                        continue;
                    }

                    // Update properties
                    routeCategory.Title = routeCategoryRequest.Title;
                    routeCategory.Description = routeCategoryRequest.Description;
                    routeCategory.Icon = routeCategoryRequest.Icon;
                    routeCategory.Status = (Domain.Enums.RouteCategoryStatus)routeCategoryRequest.Status;

                    _routeCategoryRepository.Update(routeCategory);
                }

                await _uow.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("RouteCategories updated successfully");
                return ActionResponse<bool>.Success(true, StatusCode.Ok);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"RouteCategory update failed due to exception: {ex}");
                return ActionResponse<bool>.Fail(ex.ToString(), StatusCode.BadRequest);
            }
        }
    }
}
