using MediatR;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Domain.Enums;

namespace TmobCore.Cms.Application.Features.InfoField.Commands.UpdateInfoField
{
    public class UpdateInfoFieldCommand : BaseRequest, IRequest<ActionResponse<Guid>>
    {
        public Guid Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Slug { get; set; } = string.Empty;
        public int Order { get; set; }
        public InfoFieldType Type { get; set; }
        public DateTime StartDate { get; set; } = DateTime.MinValue;
        public DateTime EndDate { get; set; } = DateTime.MinValue;
        public DateTime PublishDate { get; set; } = DateTime.MinValue;        
    }
}