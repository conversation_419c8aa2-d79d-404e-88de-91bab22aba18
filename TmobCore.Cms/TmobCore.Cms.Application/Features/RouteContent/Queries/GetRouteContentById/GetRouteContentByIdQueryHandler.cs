using AutoMapper;
using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.RouteContent;

namespace TmobCore.Cms.Application.Features.RouteContent.Queries.GetRouteContentById
{
    public class GetRouteContentByIdQueryHandler : IRequestHandler<GetRouteContentByIdQuery, ActionResponse<RouteContentResponse>>
    {
        private readonly IRouteContentRepository _routeContentRepository;
        private readonly IMapper _mapper;
        private readonly IAppLogger<GetRouteContentByIdQueryHandler> _logger;

        public GetRouteContentByIdQueryHandler(
            IRouteContentRepository routeContentRepository,
            IMapper mapper,
            IAppLogger<GetRouteContentByIdQueryHandler> logger)
        {
            _routeContentRepository = routeContentRepository;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<ActionResponse<RouteContentResponse>> Handle(GetRouteContentByIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var routeContent = await _routeContentRepository.GetRouteContentByIdAsync(request.Id, cancellationToken);
                
                if (routeContent != null)
                {
                    var data = _mapper.Map<RouteContentResponse>(routeContent);
                    _logger.LogInformation($"RouteContent found successfully for request id {request.Id}");
                    return ActionResponse<RouteContentResponse>.Success(data, StatusCode.Ok);
                }

                _logger.LogWarning($"RouteContent not found or deleted for request id {request.Id}");
                return ActionResponse<RouteContentResponse>.Fail($"RouteContent not found for this id {request.Id}", StatusCode.NotFound);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"RouteContent retrieval failed due to exception: {ex}");
                return ActionResponse<RouteContentResponse>.Fail(ex.ToString(), StatusCode.BadRequest);
            }
        }
    }
}
