using AutoMapper;
using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Contracts.User;
using TmobCore.Cms.Application.Helper;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.RouteContent;

namespace TmobCore.Cms.Application.Features.RouteContent.Queries.GetRouteContents
{
    public class GetRouteContentsQueryHandler : IRequestHandler<GetRouteContentsQuery, ActionResponse<List<RouteContentResponse>>>
    {
        private readonly IRouteContentRepository _routeContentRepository;
        private readonly IUserPrincipal _userPrincipal;
        private readonly IMapper _mapper;
        private readonly IAppLogger<GetRouteContentsQueryHandler> _logger;

        public GetRouteContentsQueryHandler(
            IRouteContentRepository routeContentRepository,
            IUserPrincipal userPrincipal,
            IMapper mapper,
            IAppLogger<GetRouteContentsQueryHandler> logger)
        {
            _routeContentRepository = routeContentRepository;
            _userPrincipal = userPrincipal;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<ActionResponse<List<RouteContentResponse>>> Handle(GetRouteContentsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var routeContentsRequest = new RouteContentRequest
                {
                    ProjectId = _userPrincipal.ProjectId.ToGuid(),
                    SearchTerm = request.SearchTerm,
                    Page = request.Page,
                    PageSize = request.PageSize,
                    GroupId = request.GroupId,
                    LanguageId = request.LanguageId,
                    CategoryId = request.CategoryId,
                    Status = request.Status
                };

                var (routeContents, totalCount) = await _routeContentRepository.GetRouteContents(routeContentsRequest, cancellationToken);

                var data = _mapper.Map<List<RouteContentResponse>>(routeContents);
                _logger.LogInformation($"RouteContents retrieved successfully. Total count: {totalCount}");

                return ActionResponse<List<RouteContentResponse>>
                    .Success(data, StatusCode.Ok)
                    .WithPagination(request.Page, request.PageSize, totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"RouteContents retrieval failed due to exception: {ex}");
                return ActionResponse<List<RouteContentResponse>>.Fail(ex.ToString(), StatusCode.BadRequest);
            }
        }
    }
}
