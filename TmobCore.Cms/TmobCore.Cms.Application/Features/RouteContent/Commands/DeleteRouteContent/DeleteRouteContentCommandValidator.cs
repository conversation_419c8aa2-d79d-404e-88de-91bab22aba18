using FluentValidation;

namespace TmobCore.Cms.Application.Features.RouteContent.Commands.DeleteRouteContent
{
    public class DeleteRouteContentCommandValidator : AbstractValidator<DeleteRouteContentCommand>
    {
        public DeleteRouteContentCommandValidator()
        {
            RuleFor(x => x.Ids)
                .NotEmpty()
                .WithMessage("At least one ID is required");

            RuleForEach(x => x.Ids)
                .NotEmpty()
                .WithMessage("ID cannot be empty");
        }
    }
}
