using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.RouteContent.Commands.DeleteRouteContent
{
    public class DeleteRouteContentCommandHandler : IRequestHandler<DeleteRouteContentCommand, ActionResponse<int>>
    {
        private readonly IRouteContentRepository _routeContentRepository;
        private readonly IAppLogger<DeleteRouteContentCommandHandler> _logger;
        private readonly IUnitOfWork _uow;

        public DeleteRouteContentCommandHandler(
            IRouteContentRepository routeContentRepository,
            IAppLogger<DeleteRouteContentCommandHandler> logger,
            IUnitOfWork uow)
        {
            _routeContentRepository = routeContentRepository;
            _logger = logger;
            _uow = uow;
        }

        public async Task<ActionResponse<int>> Handle(DeleteRouteContentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Validation
                var validator = new DeleteRouteContentCommandValidator();
                var validationResult = await validator.ValidateAsync(request, cancellationToken);

                if (!validationResult.IsValid)
                {
                    var validationErrors = string.Join(", ", validationResult.Errors.Select(x => x.ErrorMessage));
                    _logger.LogWarning($"RouteContent deletion failed due to validation errors: {validationErrors}");
                    return ActionResponse<int>.Fail(validationErrors, StatusCode.BadRequest);
                }

                var deletedCount = 0;
                var invalidIds = new List<Guid>();

                foreach (var id in request.Ids)
                {
                    var routeContent = await _routeContentRepository.GetByIdAsync(id);
                    if (routeContent != null && !routeContent.Deleted)
                    {
                        routeContent.Deleted = true;
                        _routeContentRepository.Update(routeContent);
                        deletedCount++;
                    }
                    else
                    {
                        invalidIds.Add(id);
                    }
                }

                if (invalidIds.Any())
                {
                    _logger.LogWarning($"Some RouteContent IDs were invalid: {string.Join(", ", invalidIds)}");
                    return ActionResponse<int>.Fail($"Invalid RouteContent IDs: {string.Join(", ", invalidIds)}", StatusCode.BadRequest);
                }

                if (deletedCount == 0)
                {
                    _logger.LogWarning("No RouteContents found to delete", request.Ids);
                    return ActionResponse<int>.Fail("No RouteContents found to delete", StatusCode.NotFound);
                }

                await _uow.SaveChangesAsync(cancellationToken);
                _logger.LogInformation($"{deletedCount} RouteContents have been deleted successfully", request.Ids);

                return ActionResponse<int>.Success(deletedCount, StatusCode.Ok);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"RouteContent deletion failed due to exception: {ex}");
                return ActionResponse<int>.Fail(ex.ToString(), StatusCode.BadRequest);
            }
        }
    }
}
