using AutoMapper;
using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Contracts.User;
using TmobCore.Cms.Application.Exceptions;
using TmobCore.Cms.Application.Helper;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.RouteContent.Commands.CreateRouteContent
{
    public class CreateRouteContentCommandHandler : IRequestHandler<CreateRouteContentCommand, ActionResponse<bool>>
    {
        private readonly IRouteContentRepository _routeContentRepository;
        private readonly IRouteCategoryRepository _routeCategoryRepository;
        private readonly IUserPrincipal _userPrincipal;
        private readonly IMapper _mapper;
        private readonly IAppLogger<CreateRouteContentCommandHandler> _logger;
        private readonly IUnitOfWork _uow;

        public CreateRouteContentCommandHandler(
            IRouteContentRepository routeContentRepository,
            IRouteCategoryRepository routeCategoryRepository,
            IUserPrincipal userPrincipal,
            IMapper mapper,
            IAppLogger<CreateRouteContentCommandHandler> logger,
            IUnitOfWork uow)
        {
            _routeContentRepository = routeContentRepository;
            _routeCategoryRepository = routeCategoryRepository;
            _userPrincipal = userPrincipal;
            _mapper = mapper;
            _logger = logger;
            _uow = uow;
        }

        public async Task<ActionResponse<bool>> Handle(CreateRouteContentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var projectId = _userPrincipal.ProjectId.ToGuid()
                                ?? throw new NotFoundException("ProjectId", _userPrincipal.ProjectId);

                // Validation
                var validator = new CreateRouteContentCommandValidator();
                var validationResult = await validator.ValidateAsync(request);

                if (!validationResult.IsValid)
                {
                    var validationErrors = string.Join(", ", validationResult.Errors.Select(x => x.ErrorMessage));
                    _logger.LogWarning($"RouteContent creation failed due to validation errors: {validationErrors}");
                    return ActionResponse<bool>.Fail(validationErrors, StatusCode.BadRequest);
                }

                var routeContents = new List<Domain.Entities.RouteContent>();
                var newGroupId = Guid.NewGuid();

                foreach (var routeContentRequest in request.RouteContents)
                {
                    // Verify that the category exists
                    var category = await _routeCategoryRepository.GetByIdAsync(routeContentRequest.CategoryId);
                    if (category == null || category.Deleted)
                    {
                        _logger.LogWarning($"RouteCategory not found for CategoryId: {routeContentRequest.CategoryId}");
                        return ActionResponse<bool>.Fail($"RouteCategory not found for CategoryId: {routeContentRequest.CategoryId}", StatusCode.BadRequest);
                    }

                    var routeContent = _mapper.Map<Domain.Entities.RouteContent>(routeContentRequest);
                    routeContent.UserId = _userPrincipal.UserId;
                    routeContent.ProjectId = projectId;
                    routeContent.GroupId = routeContentRequest.GroupId ?? newGroupId;
                    routeContent.LanguageId = routeContentRequest.LanguageId;

                    routeContents.Add(routeContent);
                }

                await _routeContentRepository.CreateRangeAsync(routeContents);
                await _uow.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("RouteContents created successfully");
                return ActionResponse<bool>.Success(true, StatusCode.Ok);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"RouteContent creation failed due to exception: {ex}");
                return ActionResponse<bool>.Fail(ex.ToString(), StatusCode.BadRequest);
            }
        }
    }
}
