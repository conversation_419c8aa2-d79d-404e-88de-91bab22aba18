using MediatR;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.RouteContent.Commands.UpdateRouteContent
{
    public class UpdateRouteContentCommand : IRequest<ActionResponse<bool>>
    {
        public List<UpdateRouteContentRequest> RouteContents { get; set; } = new();
    }

    public class UpdateRouteContentRequest : BaseRequest
    {
        public Guid Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string DirectUrl { get; set; } = string.Empty;
        public string MetaTitle { get; set; } = string.Empty;
        public string MetaDescription { get; set; } = string.Empty;
        public string Keywords { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public Guid CategoryId { get; set; }
    }
}
