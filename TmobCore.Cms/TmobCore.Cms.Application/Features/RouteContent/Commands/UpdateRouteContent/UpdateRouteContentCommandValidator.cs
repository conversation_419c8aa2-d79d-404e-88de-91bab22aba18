using FluentValidation;

namespace TmobCore.Cms.Application.Features.RouteContent.Commands.UpdateRouteContent
{
    public class UpdateRouteContentCommandValidator : AbstractValidator<UpdateRouteContentCommand>
    {
        public UpdateRouteContentCommandValidator()
        {
            RuleFor(x => x.RouteContents)
                .NotEmpty()
                .WithMessage("At least one route content is required");

            RuleForEach(x => x.RouteContents).ChildRules(routeContent =>
            {
                routeContent.RuleFor(rc => rc.Id)
                    .NotEmpty()
                    .WithMessage("{PropertyName} is required");

                routeContent.RuleFor(rc => rc.Title)
                    .NotEmpty()
                    .WithMessage("{PropertyName} is required")
                    .MaximumLength(255)
                    .WithMessage("{PropertyName} must not exceed 255 characters");

                routeContent.RuleFor(rc => rc.Description)
                    .NotEmpty()
                    .WithMessage("{PropertyName} is required")
                    .MaximumLength(1000)
                    .WithMessage("{PropertyName} must not exceed 1000 characters");

                routeContent.RuleFor(rc => rc.DirectUrl)
                    .MaximumLength(500)
                    .WithMessage("{PropertyName} must not exceed 500 characters");

                routeContent.RuleFor(rc => rc.MetaTitle)
                    .MaximumLength(255)
                    .WithMessage("{PropertyName} must not exceed 255 characters");

                routeContent.RuleFor(rc => rc.MetaDescription)
                    .MaximumLength(500)
                    .WithMessage("{PropertyName} must not exceed 500 characters");

                routeContent.RuleFor(rc => rc.Keywords)
                    .MaximumLength(500)
                    .WithMessage("{PropertyName} must not exceed 500 characters");

                routeContent.RuleFor(rc => rc.CategoryId)
                    .NotEmpty()
                    .WithMessage("{PropertyName} is required");
            });
        }
    }
}
