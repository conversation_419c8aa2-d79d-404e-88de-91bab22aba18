using AutoMapper;
using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.RouteContent.Commands.UpdateRouteContent
{
    public class UpdateRouteContentCommandHandler : IRequestHandler<UpdateRouteContentCommand, ActionResponse<bool>>
    {
        private readonly IRouteContentRepository _routeContentRepository;
        private readonly IRouteCategoryRepository _routeCategoryRepository;
        private readonly IMapper _mapper;
        private readonly IAppLogger<UpdateRouteContentCommandHandler> _logger;
        private readonly IUnitOfWork _uow;

        public UpdateRouteContentCommandHandler(
            IRouteContentRepository routeContentRepository,
            IRouteCategoryRepository routeCategoryRepository,
            IMapper mapper,
            IAppLogger<UpdateRouteContentCommandHandler> logger,
            IUnitOfWork uow)
        {
            _routeContentRepository = routeContentRepository;
            _routeCategoryRepository = routeCategoryRepository;
            _mapper = mapper;
            _logger = logger;
            _uow = uow;
        }

        public async Task<ActionResponse<bool>> Handle(UpdateRouteContentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Validation
                var validator = new UpdateRouteContentCommandValidator();
                var validationResult = await validator.ValidateAsync(request);

                if (!validationResult.IsValid)
                {
                    var validationErrors = string.Join(", ", validationResult.Errors.Select(x => x.ErrorMessage));
                    _logger.LogWarning($"RouteContent update failed due to validation errors: {validationErrors}");
                    return ActionResponse<bool>.Fail(validationErrors, StatusCode.BadRequest);
                }

                foreach (var routeContentRequest in request.RouteContents)
                {
                    var existingRouteContent = await _routeContentRepository.GetByIdAsync(routeContentRequest.Id);
                    if (existingRouteContent == null || existingRouteContent.Deleted)
                    {
                        _logger.LogWarning($"RouteContent not found for Id: {routeContentRequest.Id}");
                        return ActionResponse<bool>.Fail($"RouteContent not found for Id: {routeContentRequest.Id}", StatusCode.NotFound);
                    }

                    // Verify that the category exists
                    var category = await _routeCategoryRepository.GetByIdAsync(routeContentRequest.CategoryId);
                    if (category == null || category.Deleted)
                    {
                        _logger.LogWarning($"RouteCategory not found for CategoryId: {routeContentRequest.CategoryId}");
                        return ActionResponse<bool>.Fail($"RouteCategory not found for CategoryId: {routeContentRequest.CategoryId}", StatusCode.BadRequest);
                    }

                    // Map the updated values
                    _mapper.Map(routeContentRequest, existingRouteContent);
                    
                    // Update the entity
                    _routeContentRepository.Update(existingRouteContent);
                }

                await _uow.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("RouteContents updated successfully");
                return ActionResponse<bool>.Success(true, StatusCode.Ok);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"RouteContent update failed due to exception: {ex}");
                return ActionResponse<bool>.Fail(ex.ToString(), StatusCode.BadRequest);
            }
        }
    }
}
