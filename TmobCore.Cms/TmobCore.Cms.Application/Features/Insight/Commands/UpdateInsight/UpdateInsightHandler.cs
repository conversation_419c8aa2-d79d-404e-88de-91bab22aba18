using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Contracts.User;
using TmobCore.Cms.Application.Exceptions;
using TmobCore.Cms.Application.Helper;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Application.Features.Insight.Commands.UpdateInsight
{
    public class UpdateInsightHandler : IRequestHandler<UpdateInsightCommand, ActionResponse<bool>>
    {
        private readonly IInsightRepository _insightRepository;
        private readonly IAppLogger<UpdateInsightHandler> _logger;
        private readonly IMapper _mapper;
        private readonly IUserPrincipal _userPrincipal;
        private readonly IUnitOfWork _uow;

        public UpdateInsightHandler(IInsightRepository insightRepository,
                                 IAppLogger<UpdateInsightHandler> logger,
                                 IMapper mapper,
                                 IUserPrincipal userPrincipal,
                                 IUnitOfWork uow)
        {
            _insightRepository = insightRepository;
            _userPrincipal = userPrincipal;
            _logger = logger;
            _mapper = mapper;
            _uow = uow;
        }

        public async Task<ActionResponse<bool>> Handle(UpdateInsightCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var validator = new UpdateInsightValidator(_insightRepository);
                var validationResult = await validator.ValidateAsync(request, cancellationToken);

                if (!validationResult.IsValid)
                {
                    var validationErrors = string.Join(", ", validationResult.Errors.Select(x => x.ErrorMessage));
                    _logger.LogWarning($"Insight update failed due to validation errors: {validationErrors}");

                    return ActionResponse<bool>.Fail(validationErrors, StatusCode.BadRequest);
                }

                var projectId = _userPrincipal.ProjectId.ToGuid() ?? 
                    throw new NotFoundException("ProjectId", _userPrincipal.ProjectId);

                foreach (var insightRequest in request.Insights)
                {
                    var insight = await _insightRepository.GetQuery(x => x.Id == insightRequest.Id, y => y.Contents)
                        .FirstOrDefaultAsync(cancellationToken);

                    if (insight == null)
                        return ActionResponse<bool>.Fail($"Insight not found with id: {insightRequest.Id}", StatusCode.NotFound);
                    
                    _mapper.Map(insightRequest, insight);
                    insight.ImageId = FileExtentions.HandleFileIdAssignment(insightRequest.ImageId, insight.ImageId);

                    if (insightRequest.Contents.Any())
                    {
                        await UpdateInsightContents(insight, insightRequest.Contents, projectId);
                    }

                    _insightRepository.Update(insight);
                }

                await _uow.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Insights updated successfully");
                return ActionResponse<bool>.Success(true, StatusCode.Ok);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Insight update failed due to exception errors Request: {request} - Response: {ex}");
                return ActionResponse<bool>.Fail(ex.ToString(), StatusCode.BadRequest);
            }
        }

        private async Task UpdateInsightContents(Domain.Entities.Insight insight, List<Models.Insight.InsightContentUpdateRequest> contents, Guid projectId)
        {
            contents.ForEach(data =>
            {
                var insightContent = insight.Contents.FirstOrDefault(x => x.Id == data.Id);
                if (insightContent != null)
                {
                    insightContent.FileId = FileExtentions.HandleFileIdAssignment(data.ImageId, insightContent.FileId);
                    insightContent.Content = data.Content;
                    insightContent.Order = data.Order;
                }
                else
                {
                    InsightContent insightContentEntity = new InsightContent();
                    insightContentEntity.FileId = FileExtentions.HandleFileIdAssignment(data.ImageId,null);
                    insightContentEntity.Order = data.Order;
                    insightContentEntity.Content = data.Content;
                    insightContentEntity.LanguageId = insight.LanguageId;
                    insight.Contents.Add(insightContentEntity);
                }
            });
        }
    }
}