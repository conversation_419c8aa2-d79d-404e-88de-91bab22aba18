using AutoMapper;
using MediatR;
using TmobCore.Cms.Application.Contracts.FileManagement;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Contracts.User;
using TmobCore.Cms.Application.Exceptions;
using TmobCore.Cms.Application.Helper;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.DestinationCategory.Commands.CreateDestinationCategory
{
    public class CreateDestinationCategoryCommandHandler : IRequestHandler<CreateDestinationCategoryCommand, ActionResponse<Guid>>
    {
        private readonly IDestinationCategoryRepository _destinationCategoryRepository;
        private readonly IUserPrincipal _userPrincipal;
        private readonly IMapper _mapper;
        private readonly IAppLogger<CreateDestinationCategoryCommandHandler> _logger;
        private readonly IUnitOfWork _uow;
        public CreateDestinationCategoryCommandHandler(IDestinationCategoryRepository destinationCategoryRepository,
                                 IFileManagementService fileManagementService,
                                 IMapper mapper,
                                 IUserPrincipal userPrincipal,
                                 IAppLogger<CreateDestinationCategoryCommandHandler> logger,
                                 IUnitOfWork uow)
        {
            _destinationCategoryRepository = destinationCategoryRepository;
            _mapper = mapper;
            _userPrincipal = userPrincipal;
            _logger = logger;
            _uow = uow;
        }

        public async Task<ActionResponse<Guid>> Handle(CreateDestinationCategoryCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var projectId = _userPrincipal.ProjectId.ToGuid() ?? throw new NotFoundException("ProjectId", _userPrincipal.ProjectId);

                var destinationCategory = _mapper.Map<Domain.Entities.DestinationCategory>(request);
                destinationCategory.UserId = _userPrincipal.UserId;
                destinationCategory.ProjectId = projectId;
                destinationCategory.LanguageId = request.LanguageId;
                destinationCategory.ImageId = FileExtentions.HandleFileIdAssignment(request.ImageId, null);

                await _destinationCategoryRepository.CreateAsync(destinationCategory);
                await _uow.SaveChangesAsync(cancellationToken);
                return ActionResponse<Guid>.Success(destinationCategory.Id, StatusCode.Ok);
            }
            catch (NotFoundException notFoundException)
            {
                _logger.LogWarning($"Destination Category creation failed due to exception errors {notFoundException.ToString()}");
                return ActionResponse<Guid>.Fail(notFoundException.ToString(), StatusCode.BadRequest);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Destination Category creation failed due to exception errors Request: {request} - Response: {ex.ToString()}");
                return ActionResponse<Guid>.Fail(ex.ToString(), StatusCode.BadRequest);
            }
        }
    }
}
