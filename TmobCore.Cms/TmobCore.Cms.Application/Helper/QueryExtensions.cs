using System.Linq.Expressions;
using System.Reflection;

namespace TmobCore.Cms.Application.Helper
{
    public static class QueryExtensions
    {
        /// <summary>
        /// Applies dynamic sorting to an IQueryable based on property name and sort order
        /// </summary>
        /// <typeparam name="T">The entity type</typeparam>
        /// <param name="query">The IQueryable to sort</param>
        /// <param name="sortBy">The property name to sort by</param>
        /// <param name="sortOrder">The sort order: "asc" for ascending, "desc" for descending</param>
        /// <returns>The sorted IQueryable</returns>
        public static IQueryable<T> ApplyDynamicSorting<T>(this IQueryable<T> query, string sortBy, string sortOrder)
        {
            if (string.IsNullOrWhiteSpace(sortBy))
                return query;

            var entityType = typeof(T);
            var property = GetPropertyInfo(entityType, sortBy);
            
            if (property == null)
                return query;

            var parameter = Expression.Parameter(entityType, "x");
            var propertyAccess = Expression.MakeMemberAccess(parameter, property);
            var orderByExpression = Expression.Lambda(propertyAccess, parameter);

            var methodName = string.Equals(sortOrder, "desc", StringComparison.OrdinalIgnoreCase) 
                ? "OrderByDescending" 
                : "OrderBy";

            var resultExpression = Expression.Call(
                typeof(Queryable),
                methodName,
                new Type[] { entityType, property.PropertyType },
                query.Expression,
                Expression.Quote(orderByExpression));

            return query.Provider.CreateQuery<T>(resultExpression);
        }

        /// <summary>
        /// Gets property info for a given property name, supporting nested properties
        /// </summary>
        /// <param name="type">The type to search</param>
        /// <param name="propertyName">The property name (can be nested like "Property.SubProperty")</param>
        /// <returns>PropertyInfo if found, null otherwise</returns>
        private static PropertyInfo? GetPropertyInfo(Type type, string propertyName)
        {
            var properties = propertyName.Split('.');
            PropertyInfo? property = null;
            var currentType = type;

            foreach (var prop in properties)
            {
                property = currentType.GetProperty(prop, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                if (property == null)
                    return null;
                currentType = property.PropertyType;
            }

            return property;
        }

        /// <summary>
        /// Validates if a sort field is allowed for the Image entity
        /// </summary>
        /// <param name="sortBy">The field name to validate</param>
        /// <returns>True if the field is allowed for sorting</returns>
        public static bool IsValidImageSortField(string? sortBy)
        {
            if (string.IsNullOrWhiteSpace(sortBy))
                return false;

            var allowedFields = new[] { "DateCreated", "DateModified", "PublishDate" };
            return allowedFields.Contains(sortBy, StringComparer.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Validates if a sort order is valid
        /// </summary>
        /// <param name="sortOrder">The sort order to validate</param>
        /// <returns>True if the sort order is valid</returns>
        public static bool IsValidSortOrder(string? sortOrder)
        {
            if (string.IsNullOrWhiteSpace(sortOrder))
                return false;

            return string.Equals(sortOrder, "asc", StringComparison.OrdinalIgnoreCase) ||
                   string.Equals(sortOrder, "desc", StringComparison.OrdinalIgnoreCase);
        }
    }
}
