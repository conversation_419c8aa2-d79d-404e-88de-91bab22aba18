using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TmobCore.Cms.Domain.Common;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Infrastructure.Database.Configurations;

public class StoryConfiguration : IEntityTypeConfiguration<Story>
{
    public void Configure(EntityTypeBuilder<Story> builder)
    {
        builder.HasKey(s => s.Id);
        builder.Property(s => s.StartDate).IsRequired();
        builder.Property(s => s.EndDate).IsRequired();
        builder.Property(s => s.Order).HasDefaultValue(0);
        builder.Property(s => s.Status).HasDefaultValue(BaseStatus.Draft);
        builder.Property(s => s.Deleted).HasDefaultValue(false);
        builder.Property(s => s.Tags).HasMaxLength(255);
        builder.Property(s => s.CtaButtonLink).HasMaxLength(255);

        builder.HasOne(s => s.Category)
            .WithMany(sc => sc.Stories)
            .HasForeignKey(s => s.CategoryId);
        
        builder.HasOne(s => s.Image)
            .WithMany()
            .HasForeignKey(s => s.ImageId)
            .OnDelete(DeleteBehavior.SetNull);    
        
        builder.Property(s => s.Slug).IsRequired();
    }
}