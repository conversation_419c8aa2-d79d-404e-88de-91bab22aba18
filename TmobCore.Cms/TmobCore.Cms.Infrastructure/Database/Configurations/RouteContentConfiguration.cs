using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TmobCore.Cms.Domain.Entities;
using TmobCore.Cms.Domain.Enums;

namespace TmobCore.Cms.Infrastructure.Database.Configurations
{
    public class RouteContentConfiguration : IEntityTypeConfiguration<RouteContent>
    {
        public void Configure(EntityTypeBuilder<RouteContent> builder)
        {
            builder.HasKey(rc => rc.Id);
            
            // String properties with constraints
            builder.Property(rc => rc.Title)
                .IsRequired()
                .HasMaxLength(255);
                
            builder.Property(rc => rc.Description)
                .IsRequired()
                .HasMaxLength(1000);
                
            builder.Property(rc => rc.DirectUrl)
                .IsRequired()
                .HasMaxLength(500);
                
            builder.Property(rc => rc.MetaTitle)
                .IsRequired()
                .HasMaxLength(255);
                
            builder.Property(rc => rc.MetaDescription)
                .IsRequired()
                .HasMaxLength(500);
                
            builder.Property(rc => rc.Keywords)
                .IsRequired()
                .HasMaxLength(500);
                
            builder.Property(rc => rc.Content)
                .IsRequired();
            
            // Default values
            builder.Property(rc => rc.Status)
                .HasDefaultValue(RouteCategoryStatus.Draft);
                
            builder.Property(rc => rc.Deleted)
                .HasDefaultValue(false);
            
            // Required fields
            builder.Property(rc => rc.ProjectId)
                .IsRequired();
                
            builder.Property(rc => rc.UserId)
                .IsRequired();
                
            builder.Property(rc => rc.GroupId)
                .IsRequired();
                
            builder.Property(rc => rc.CategoryId)
                .IsRequired();
            
            // Foreign key relationship with RouteCategory
            builder.HasOne(rc => rc.Category)
                .WithMany(cat => cat.RouteContents)
                .HasForeignKey(rc => rc.CategoryId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
