using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TmobCore.Cms.Domain.Entities;
using TmobCore.Cms.Domain.Enums;

namespace TmobCore.Cms.Infrastructure.Database.Configurations
{
    public class RouteCategoryConfiguration : IEntityTypeConfiguration<RouteCategory>
    {
        public void Configure(EntityTypeBuilder<RouteCategory> builder)
        {
            builder.HasKey(rc => rc.Id);
            
            // String properties with constraints
            builder.Property(rc => rc.Title)
                .IsRequired()
                .HasMaxLength(255);
                
            builder.Property(rc => rc.Description)
                .IsRequired()
                .HasMaxLength(1000);
                
            builder.Property(rc => rc.Icon)
                .IsRequired()
                .HasMaxLength(255);
            
            // Default values
            builder.Property(rc => rc.Status)
                .HasDefaultValue(RouteCategoryStatus.Draft);
                
            builder.Property(rc => rc.Deleted)
                .HasDefaultValue(false);
            
            // Required fields
            builder.Property(rc => rc.ProjectId)
                .IsRequired();
                
            builder.Property(rc => rc.UserId)
                .IsRequired();
                
            builder.Property(rc => rc.GroupId)
                .IsRequired();
                
            builder.Property(rc => rc.LanguageId)
                .IsRequired();
        }
    }
}
