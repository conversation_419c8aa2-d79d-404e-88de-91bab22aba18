using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Domain.Common;
using TmobCore.Cms.Domain.Entities;
using File = TmobCore.Cms.Domain.Entities.File;

namespace TmobCore.Cms.Infrastructure.Database.DatabaseContext;

public class CmsCoreDatabaseContext : IdentityDbContext<User, Role, string>
{
    public CmsCoreDatabaseContext(DbContextOptions<CmsCoreDatabaseContext> options) : base(options)
    {

    }

    public DbSet<Example> Example { get; set; }
    public DbSet<Campaign> Campaign { get; set; }
    public DbSet<CampaignType> CampaignTypes { get; set; }
    public DbSet<CampaignCode> CampaignCodes { get; set; }
    public DbSet<Component> Components { get; set; }
    public DbSet<Blog> Blogs { get; set; }
    public DbSet<BlogContent> BlogContents { get; set; }
    public DbSet<BlogFilter> BlogFilters { get; set; }
    public DbSet<Image> Images { get; set; }
    public DbSet<Destination> Destinations { get; set; }
    public DbSet<DestinationContent> DestinationContents { get; set; }
    public DbSet<DestinationPrice> DestinationPrices { get; set; }
    
    public DbSet<Faq> Faqs { get; set; }
    public DbSet<Insight> Insights { get; set; }
    public DbSet<InsightContent> InsightContents { get; set; }
    public DbSet<Story> Stories { get; set; }
    public DbSet<StoryCategory> StoryCategories { get; set; }
    public DbSet<Project> Projects { get; set; }
    public DbSet<UserProjectRole> UserProjectRoles { get; set; }
    public DbSet<Language> Languages { get; set; }
    public DbSet<CmsKey> CmsKeys { get; set; }
    public DbSet<CmsKeyValue> CmsKeyValues { get; set; }
    public DbSet<InfoField> InfoFields { get; set; }
    public DbSet<Slider> Sliders { get; set; }
    public DbSet<Service> Services { get; set; }
    public DbSet<ServiceContent> ServiceContents { get; set; }
    public DbSet<Page> Pages { get; set; }
    public DbSet<PageContent> PageContents { get; set; }
    public DbSet<PageComponent> PageComponents { get; set; }
    public DbSet<File> Files { get; set; }
    public DbSet<Folder> Folders {  get; set; }
    public DbSet<Airport> Airports { get; set; }
    public DbSet<Country> Countries { get; set; }
    public DbSet<City> Cities { get; set; }
    public DbSet<DestinationCategory> DestinationCategories { get; set; }
    public DbSet<DestinationContentFaq> DestinationContentFaqs { get; set; }
    public DbSet<News> News { get; set; }
    public DbSet<NewsContent> NewsContents { get; set; }
    
    public DbSet<SpecialOfferWidget> SpecialOfferWidgets { get; set; }
    public DbSet<PriceInCurrency> PriceInCurrencies { get; set; }
    
    public DbSet<DiscoverOfferWidget> DiscoverOfferWidgets { get; set; }
    public DbSet<DiscoverOfferRoute> DiscoverOfferRoutes { get; set; }
    public DbSet<DiscoverOfferPrice> DiscoverOfferPrices { get; set; }

    public DbSet<CustomizableOfferWidget> CustomizableOfferWidgets { get; set; }
    public DbSet<CustomizableOfferRoute> CustomizableOfferRoutes { get; set; }
    public DbSet<CustomizableOfferPrice> CustomizableOfferPrices { get; set; }

    public DbSet<ExploreOfferWidget> ExploreOfferWidgets { get; set; }
    public DbSet<ExploreOfferRoute> ExploreOfferRoutes { get; set; }
    public DbSet<ExploreOfferPrice> ExploreOfferPrices { get; set; }

    public DbSet<RoundtripOfferWidget> RoundtripOfferWidgets { get; set; } = null!;
    
    public DbSet<HelpArticle> HelpArticles { get; set; } = null!;
    public DbSet<HelpCategory> HelpCategories { get; set; } = null!;

    public DbSet<AgencyPermission> AgencyPermissions { get; set; }
    public DbSet<AgencyDefaultPermission> AgencyDefaultPermissions { get; set; }

    public DbSet<RouteCategory> RouteCategories { get; set; }
    public DbSet<RouteContent> RouteContents { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(CmsCoreDatabaseContext).Assembly);
        base.OnModelCreating(modelBuilder);
    }

    public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        foreach (var entry in base.ChangeTracker.Entries<BaseEntity>()
            .Where(q => q.State == EntityState.Added || q.State == EntityState.Modified))
        {
            entry.Entity.DateModified = DateTime.Now;

            if (entry.State is EntityState.Added)
            {
                entry.Entity.DateCreated = DateTime.Now;
            }
        }

        return base.SaveChangesAsync(cancellationToken);
    }
}