using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Domain.Entities;
using TmobCore.Cms.Infrastructure.Database.DatabaseContext;

namespace TmobCore.Cms.Infrastructure.Database.Repositories
{
    public class ServiceRepository : GenericRepository<Service>, IServiceRepository
    {
        public ServiceRepository(CmsCoreDatabaseContext context) : base(context)
        {
        }

        public async Task<bool> IsSlugUnique(string slug, Guid? id = null)
        {
            var query = _context.Services.AsQueryable();
            if (id.HasValue)
                query = query.Where(x => x.Id != id);

            return !await query.AnyAsync(x => x.Slug == slug && !x.Deleted);
        }   
    }
}