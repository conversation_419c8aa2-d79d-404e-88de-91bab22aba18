using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.RouteContent;
using TmobCore.Cms.Domain.Entities;
using TmobCore.Cms.Infrastructure.Database.DatabaseContext;

namespace TmobCore.Cms.Infrastructure.Database.Repositories
{
    public class RouteContentRepository : GenericRepository<RouteContent>, IRouteContentRepository
    {
        public RouteContentRepository(CmsCoreDatabaseContext context) : base(context)
        {
        }

        public async Task<(List<RouteContent> RouteContents, int TotalCount)> GetRouteContents(RouteContentRequest request, CancellationToken cancellationToken)
        {
            var query = _context.RouteContents
                .AsNoTracking()
                .Where(x => !x.Deleted);

            if (request.ProjectId != null)
                query = query.Where(x => x.ProjectId == request.ProjectId);

            if (request.GroupId.HasValue)
                query = query.Where(x => x.GroupId == request.GroupId);

            if (request.LanguageId.HasValue)
                query = query.Where(x => x.LanguageId == request.LanguageId);

            if (request.CategoryId.HasValue)
                query = query.Where(x => x.CategoryId == request.CategoryId);

            if (request.Status.HasValue)
                query = query.Where(x => x.Status == request.Status.Value);

            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                query = query.Where(x =>
                    x.Title.Contains(request.SearchTerm) ||
                    x.Description.Contains(request.SearchTerm) ||
                    x.Content.Contains(request.SearchTerm) ||
                    x.MetaTitle.Contains(request.SearchTerm) ||
                    x.MetaDescription.Contains(request.SearchTerm) ||
                    x.Keywords.Contains(request.SearchTerm));
            }

            var totalCount = await query.CountAsync(cancellationToken);

            if (request.PageSize > 0)
            {
                query = query.Skip((request.Page - 1) * request.PageSize)
                           .Take(request.PageSize);
            }

            var routeContents = await query
                .Include(x => x.Category)
                .OrderBy(x => x.Title)
                .ToListAsync(cancellationToken);

            return (routeContents, totalCount);
        }

        public async Task<RouteContent> GetRouteContentByIdAsync(Guid? routeContentId, CancellationToken cancellationToken)
        {
            var routeContent = await _context.RouteContents
                .AsNoTracking()
                .Include(x => x.Category)
                .Where(x => x.Id == routeContentId && !x.Deleted)
                .FirstOrDefaultAsync(cancellationToken);

            return routeContent;
        }

        public async Task<List<RouteContent>> GetRouteContentByGroupIdAsync(Guid groupId)
        {
            var routeContents = await _context.RouteContents
                .Where(x => x.GroupId == groupId && !x.Deleted)
                .Include(x => x.Category)
                .ToListAsync();
            
            return routeContents;
        }

        public async Task<List<RouteContent>> GetRouteContentByCategoryIdAsync(Guid categoryId)
        {
            var routeContents = await _context.RouteContents
                .Where(x => x.CategoryId == categoryId && !x.Deleted)
                .Include(x => x.Category)
                .ToListAsync();
            
            return routeContents;
        }
    }
}
