using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.News;
using TmobCore.Cms.Domain.Entities;
using TmobCore.Cms.Infrastructure.Database.DatabaseContext;

namespace TmobCore.Cms.Infrastructure.Database.Repositories;

public class NewsRepository: GenericRepository<News>, INewsRepository
{
    public NewsRepository(CmsCoreDatabaseContext context) : base(context)
    {
        
    }

    public async Task<bool> IsSlugUnique(string slug, Guid? id = null)
    {
        var query = _context.News.AsQueryable();
        if (id.HasValue)
            query = query.Where(x => x.Id != id);

        return !await query.AnyAsync(x => x.Slug == slug && !x.Deleted);
    }

    public async Task<(List<News> News, int TotalCount)> GetNews(NewsRequest request, CancellationToken cancellationToken)
    {
        var query = _context.News
            .AsNoTracking()
            .Include(x=> x.Image)
            .Include(x => x.NewsContents)
            .ThenInclude(x => x.Image)
            .Where(x => !x.Deleted && (request.ProjectId == null || x.ProjectId == request.ProjectId) 
                                   && (!request.GroupId.HasValue || x.GroupId == request.GroupId.Value));

        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            query = query.Where(x =>
                x.Title.Contains(request.SearchTerm) ||
                x.NewsContents.Any(nc => nc.Content.Contains(request.SearchTerm)));
        }

        // EF Core'da GroupBy desteklenmediği için ToList alıp sonra gruplama yapılır
        var resultList = await query.ToListAsync(cancellationToken);

        var groupedNews = resultList
            .GroupBy(x => x.GroupId)
            .SelectMany(g => g)
            .ToList();

        var totalCount = groupedNews.Count();

        var pagedResult = groupedNews
            .Skip((request.Page - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToList();

        return (pagedResult, totalCount);
    }
    
    public async Task<News?> GetByIdWithContentsAsync(Guid id, CancellationToken cancellationToken)
    {
        return await _context.News
            .Include(x => x.NewsContents)
            .ThenInclude(x => x.Image)
            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
    }
}