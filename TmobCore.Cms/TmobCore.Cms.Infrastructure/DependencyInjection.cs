using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using TmobCore.Cms.Application.Contracts.Excel;
using TmobCore.Cms.Application.Contracts.FileManagement;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Mail;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Contracts.User;
using TmobCore.Cms.Application.Identity;
using TmobCore.Cms.Application.Models.Common.Settings;
using TmobCore.Cms.Application.Models.FileManagement.Settings;
using TmobCore.Cms.Application.Models.Identity;
using TmobCore.Cms.Application.Providers;
using TmobCore.Cms.Domain.Entities;
using TmobCore.Cms.Infrastructure.Database.DatabaseContext;
using TmobCore.Cms.Infrastructure.Database.Repositories;
using TmobCore.Cms.Infrastructure.Excel;
using TmobCore.Cms.Infrastructure.FileManagement;
using TmobCore.Cms.Infrastructure.Identity.Services;
using TmobCore.Cms.Infrastructure.Logging;
using TmobCore.Cms.Infrastructure.Mail;

namespace TmobCore.Cms.Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddDbContext<CmsCoreDatabaseContext>(options =>
        {
            options.UseSqlServer(configuration.GetConnectionString("CmsCoreDatabaseConnectionString"));
        });

        //services.AddDbContext<CmsCoreIdentityDbContext>(options =>
        //{
        //    options.UseSqlServer(configuration.GetConnectionString("CmsCoreDatabaseConnectionString"));
        //});

        services.AddIdentity<User, Role>()
            .AddEntityFrameworkStores<CmsCoreDatabaseContext>()
            .AddDefaultTokenProviders();

        services.Configure<JwtSettings>(configuration.GetSection("JwtSettings"));

        services.AddTransient<IAuthService, AuthService>();

        services.AddAuthentication(options =>
        {
            options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
            options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
        }).AddJwtBearer(o =>
        {
            o.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero,
                ValidIssuer = configuration["JwtSettings:Issuer"],
                ValidAudience = configuration["JwtSettings:Audience"],
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["JwtSettings:Key"]))
            };
        });

        services.AddScoped(typeof(IGenericRepository<>), typeof(GenericRepository<>));
        services.AddScoped<IUnitOfWork, UnitOfWork>();
        services.AddScoped<IExampleRepository, ExampleRepository>();
        services.AddScoped<ICampaignRepository, CampaignRepository>();
        services.AddScoped<ICampaignTypeRepository, CampaignTypeRepository>();
        services.AddScoped<IComponentRepository, ComponentRepository>();
        services.AddScoped<IDestinationRepository, DestinationRepository>();
        services.AddScoped<IBlogRepository, BlogRepository>();
        services.AddScoped<IBlogFilterRepository, BlogFilterRepository>();
        services.AddScoped<IStoryRepository, StoryRepository>();
        services.AddScoped<IStoryCategoryRepository, StoryCategoryRepository>();
        services.AddScoped<ISliderRepository, SliderRepository>();
        services.AddScoped<IMemberRepository, MemberRepository>();
        services.AddScoped<IProfileRepository, ProfileRepository>();
        services.AddScoped<IInsightRepository, InsightRepository>();
        services.AddScoped<IAirportRepository, AirportRepository>();
        services.AddScoped<ICityRepository, CityRepository>();
        services.AddScoped<ICountryRepository, CountryRepository>();
        services.AddScoped<IDestinationCategoryRepository, DestinationCategoryRepository>();

        services.AddScoped<IInfoFieldRepository, InfoFieldRepository>();
        services.AddScoped<IServiceRepository, ServiceRepository>();

        services.AddScoped(typeof(IAppLogger<>), typeof(LoggerAdapter<>));

        services.AddScoped<IExcelService, EPPlusExcelService>();
        services.AddScoped<IFileManagementService, FileManagementService>();
        services.AddScoped<IMailService,SmtpEmailService>();
        services.AddScoped<IFaqRepository, FaqRepository>();
        services.AddScoped<IImageRepository, ImageRepository>();
        services.AddScoped<ILanguageRepository, LanguageRepository>();
        services.AddScoped<ICmsKeyRepository, CmsKeyRepository>();
        services.AddScoped<ICmsKeyValueRepository, CmsKeyValueRepository>();
        services.AddScoped<IPageRepository, PageRepository>();
        services.AddScoped<IPageContentRepository, PageContentRepository>();
        services.AddScoped<IPageComponentRepository, PageComponentRepository>();
        services.AddScoped<IFileRepository, FileRepository>();
        services.AddScoped<IFolderRepository, FolderRepository>();
        services.AddScoped<INewsRepository, NewsRepository>();
      
        services.AddScoped<ISpecialOfferWidgetRepository, SpecialOfferWidgetRepository>();
        services.AddScoped<IDiscoverOfferWidgetRepository, DiscoverOfferWidgetRepository>();

        services.AddScoped<ICustomizableOfferWidgetRepository, CustomizableOfferWidgetRepository>();

        services.AddScoped<IExploreOfferWidgetRepository, ExploreOfferWidgetRepository>();

        services.AddScoped<IRoundtripOfferWidgetRepository, RoundtripOfferWidgetRepository>();

        services.AddScoped<IHelpArticleRepository, HelpArticleRepository>();
        services.AddScoped<IHelpCategoryRepository, HelpCategoryRepository>();
        services.AddScoped<IBreadcrumbService, BreadcrumbService>();
        services.AddScoped<IRouteCategoryRepository, RouteCategoryRepository>();
        services.AddScoped<IRouteContentRepository, RouteContentRepository>();

        services.AddScoped<IAgencyPermissionRepository, AgencyPermissionRepository>();
        
        services.Configure<FileManagementSettings>(configuration.GetSection("FileManagementSettings"));
        services.Configure<GeneralSettings>(configuration.GetSection("GeneralSettings"));
        services.Configure<SmtpSettings>(configuration.GetSection("SmtpSettings"));

        return services;
    }
}
