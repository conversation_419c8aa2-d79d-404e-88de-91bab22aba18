using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TmobCore.Cms.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddRouteCategoryEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Example",
                keyColumn: "Id",
                keyValue: new Guid("ba2db1e8-93d9-43ca-a94e-c5f2eaa92b9b"));

            migrationBuilder.CreateTable(
                name: "RouteCategories",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ProjectId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    GroupId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    Title = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    Icon = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DateCreated = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DateModified = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LanguageId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RouteCategories", x => x.Id);
                });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "302dffb7-ba4e-44e2-b77a-1b258954f9d5",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "6f0fa56e-fec6-486e-bc2d-e31586327621", "AQAAAAIAAYagAAAAENhUOj9bhsj7KCwtJOGzRlK6nCMRybUTFkrc8u/Kb9P86udzAYPabMpPD5as0yGFww==", "560e7edc-b862-4857-b01a-310e97e04a6b" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "b28b07da-03a3-435a-a370-2a675c11a57d",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "6e882266-0a8b-4f3e-9bc8-66ca22bdb53c", "AQAAAAIAAYagAAAAEBD6XynmiJIQXf/mbznsPnT1YxQQI7+HuuDTetIDtiqyBE6YN+pOwIbFm2w9yEwWYQ==", "45d0d808-a57f-42b9-8691-46cca8dbb469" });

            migrationBuilder.InsertData(
                table: "Example",
                columns: new[] { "Id", "Count", "DateCreated", "DateModified", "Description", "LanguageId", "Name" },
                values: new object[] { new Guid("3509a9b9-c422-4d6a-9642-eb934967e97e"), 13, new DateTime(2025, 7, 5, 14, 3, 9, 1, DateTimeKind.Local).AddTicks(3270), new DateTime(2025, 7, 5, 14, 3, 9, 1, DateTimeKind.Local).AddTicks(3300), "The modelBuilder.Entity<Example>().HasData() method in Entity Framework is used to seed initial data into the database for a specific entity (in this case, the \"Example\" entity).", null, "First Data" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "RouteCategories");

            migrationBuilder.DeleteData(
                table: "Example",
                keyColumn: "Id",
                keyValue: new Guid("3509a9b9-c422-4d6a-9642-eb934967e97e"));

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "302dffb7-ba4e-44e2-b77a-1b258954f9d5",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "3c58e35b-3250-4b39-8491-54fc91f73c84", "AQAAAAIAAYagAAAAECt901J41raV/wig63SGwN0Cra9+IbRVeP/wXJ2N6+Znw8utCEKY8yU1gudciLWhag==", "e50c992e-06bd-42ed-8034-245dc6c60b2b" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "b28b07da-03a3-435a-a370-2a675c11a57d",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "2ec39fbb-9a24-4dcd-b61e-e07ade692a92", "AQAAAAIAAYagAAAAEKoYGLJmiTPL8L2fSgrc6GZNOQiVxYklKrpH7tnj6w91B6LGqaQ7v/1EbkspFyuhLQ==", "2350b745-df89-44ca-8db3-01cfdcdfa9a6" });

            migrationBuilder.InsertData(
                table: "Example",
                columns: new[] { "Id", "Count", "DateCreated", "DateModified", "Description", "LanguageId", "Name" },
                values: new object[] { new Guid("ba2db1e8-93d9-43ca-a94e-c5f2eaa92b9b"), 13, new DateTime(2025, 7, 4, 13, 48, 29, 979, DateTimeKind.Local).AddTicks(5810), new DateTime(2025, 7, 4, 13, 48, 29, 979, DateTimeKind.Local).AddTicks(5860), "The modelBuilder.Entity<Example>().HasData() method in Entity Framework is used to seed initial data into the database for a specific entity (in this case, the \"Example\" entity).", null, "First Data" });
        }
    }
}
