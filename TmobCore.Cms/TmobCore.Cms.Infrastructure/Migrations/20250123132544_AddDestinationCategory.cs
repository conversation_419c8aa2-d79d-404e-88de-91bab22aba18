using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TmobCore.Cms.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddDestinationCategory : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Example",
                keyColumn: "Id",
                keyValue: new Guid("644ea9a3-4255-47ec-9b41-1452abaee7a8"));

            migrationBuilder.AddColumn<Guid>(
                name: "FileId",
                table: "DestinationCategories",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "302dffb7-ba4e-44e2-b77a-1b258954f9d5",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "2af05e4d-9f06-4cad-97bb-b32fc841b197", "AQAAAAIAAYagAAAAEFRVfEyDP5PLu0zV8aNxtc0PXtBxPA0RRM+Ud7L2Gqs318pCko95ZoKR8kSZrgmOTQ==", "2c784a48-6e65-476d-8661-539e95288696" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "b28b07da-03a3-435a-a370-2a675c11a57d",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "c9c169cf-257e-4017-a737-c675c553c378", "AQAAAAIAAYagAAAAEByAJAR33tPAQ8l9GHQE+yvSog/mwzWZd/qj56uGc9dn3obp6SdGlJ2KlaTnH5IRRA==", "c560ad2d-dbb0-409b-846f-ac2133fe06d9" });

            migrationBuilder.InsertData(
                table: "Example",
                columns: new[] { "Id", "Count", "DateCreated", "DateModified", "Description", "LanguageId", "Name" },
                values: new object[] { new Guid("4dab4d6c-1471-4e71-8298-fc70c36dc31f"), 13, new DateTime(2025, 1, 23, 16, 25, 44, 479, DateTimeKind.Local).AddTicks(3442), new DateTime(2025, 1, 23, 16, 25, 44, 479, DateTimeKind.Local).AddTicks(3458), "The modelBuilder.Entity<Example>().HasData() method in Entity Framework is used to seed initial data into the database for a specific entity (in this case, the \"Example\" entity).", null, "First Data" });

            migrationBuilder.CreateIndex(
                name: "IX_DestinationCategories_FileId",
                table: "DestinationCategories",
                column: "FileId");

            migrationBuilder.AddForeignKey(
                name: "FK_DestinationCategories_Files_FileId",
                table: "DestinationCategories",
                column: "FileId",
                principalTable: "Files",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DestinationCategories_Files_FileId",
                table: "DestinationCategories");

            migrationBuilder.DropIndex(
                name: "IX_DestinationCategories_FileId",
                table: "DestinationCategories");

            migrationBuilder.DeleteData(
                table: "Example",
                keyColumn: "Id",
                keyValue: new Guid("4dab4d6c-1471-4e71-8298-fc70c36dc31f"));

            migrationBuilder.DropColumn(
                name: "FileId",
                table: "DestinationCategories");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "302dffb7-ba4e-44e2-b77a-1b258954f9d5",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "a9b169c1-a43d-4799-896c-34d9d6eb3262", "AQAAAAIAAYagAAAAEOD0dQZpl4UePisyNq6FZQDX/EchBwpGv04Ed3q+j3k6DHkueMHJ7TiCjDstpuxPOw==", "53c6bad9-98d4-4940-97ea-68e660a69c4a" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "b28b07da-03a3-435a-a370-2a675c11a57d",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "031bbe81-9616-48f5-8911-93d7a6a86a7e", "AQAAAAIAAYagAAAAEHD89tmJ3aroEIkF3F3AHaSnScGxMw6AzsNXwxBxyHsspDtHImnUVOsktDp6l60h+w==", "f3563e88-7e73-47a5-9c28-e6bc14498964" });

            migrationBuilder.InsertData(
                table: "Example",
                columns: new[] { "Id", "Count", "DateCreated", "DateModified", "Description", "LanguageId", "Name" },
                values: new object[] { new Guid("644ea9a3-4255-47ec-9b41-1452abaee7a8"), 13, new DateTime(2025, 1, 23, 16, 4, 54, 848, DateTimeKind.Local).AddTicks(6682), new DateTime(2025, 1, 23, 16, 4, 54, 848, DateTimeKind.Local).AddTicks(6697), "The modelBuilder.Entity<Example>().HasData() method in Entity Framework is used to seed initial data into the database for a specific entity (in this case, the \"Example\" entity).", null, "First Data" });
        }
    }
}
