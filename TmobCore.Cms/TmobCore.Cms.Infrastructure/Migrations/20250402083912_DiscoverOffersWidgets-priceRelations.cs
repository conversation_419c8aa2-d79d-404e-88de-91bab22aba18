using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TmobCore.Cms.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class DiscoverOffersWidgetspriceRelations : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Example",
                keyColumn: "Id",
                keyValue: new Guid("dba10f8a-bd49-496e-ae49-080dd475ee82"));

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "302dffb7-ba4e-44e2-b77a-1b258954f9d5",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "92344ab2-f555-4494-9cbe-6a1b3d9997dc", "AQAAAAIAAYagAAAAEK8uOER4tFmE808rqur+dd0lYl7HYawQcgRPrcviO813h5SZrYkKvwILl/Cp3cLKGg==", "2bba2882-baaa-439b-b782-259bb7920538" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "b28b07da-03a3-435a-a370-2a675c11a57d",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "8997f552-54b8-4a36-b1e5-78afc0cfb7a5", "AQAAAAIAAYagAAAAEEQS+nVlGgLFsC74onOBBEquliaeDW0si+TZvLDUqGliuxeq/3fuUeAlZ6xG+/LPFA==", "d124648a-1db4-464c-b93e-2ec268b0365b" });

            migrationBuilder.InsertData(
                table: "Example",
                columns: new[] { "Id", "Count", "DateCreated", "DateModified", "Description", "LanguageId", "Name" },
                values: new object[] { new Guid("b570b0db-99ea-44b1-98ec-b72d307ebd89"), 13, new DateTime(2025, 4, 2, 11, 39, 11, 776, DateTimeKind.Local).AddTicks(2030), new DateTime(2025, 4, 2, 11, 39, 11, 776, DateTimeKind.Local).AddTicks(2070), "The modelBuilder.Entity<Example>().HasData() method in Entity Framework is used to seed initial data into the database for a specific entity (in this case, the \"Example\" entity).", null, "First Data" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Example",
                keyColumn: "Id",
                keyValue: new Guid("b570b0db-99ea-44b1-98ec-b72d307ebd89"));

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "302dffb7-ba4e-44e2-b77a-1b258954f9d5",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "8d1963be-512b-46eb-bbc5-72c978647414", "AQAAAAIAAYagAAAAEHi92aczAwiI7s4UPEvmITOvYxkhgc0vsjghXjUNDGq9hgkMHKo1Bl8YZ7WRbQ+Tew==", "94643a46-745d-491a-aa1f-b7ea376b5eee" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "b28b07da-03a3-435a-a370-2a675c11a57d",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "0d72a478-3546-46ca-b034-1d740bcb3581", "AQAAAAIAAYagAAAAEOZ9HGS3kd8zNDAE3YWK2CbXyLskfENMy0ootDOV9QpZjY7IEaz7z4eKIigxzaEIrg==", "77e38fb1-73ba-422a-b573-a1872101c8fe" });

            migrationBuilder.InsertData(
                table: "Example",
                columns: new[] { "Id", "Count", "DateCreated", "DateModified", "Description", "LanguageId", "Name" },
                values: new object[] { new Guid("dba10f8a-bd49-496e-ae49-080dd475ee82"), 13, new DateTime(2025, 4, 2, 9, 12, 51, 502, DateTimeKind.Local).AddTicks(3410), new DateTime(2025, 4, 2, 9, 12, 51, 502, DateTimeKind.Local).AddTicks(3440), "The modelBuilder.Entity<Example>().HasData() method in Entity Framework is used to seed initial data into the database for a specific entity (in this case, the \"Example\" entity).", null, "First Data" });
        }
    }
}
