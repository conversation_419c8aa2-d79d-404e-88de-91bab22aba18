using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TmobCore.Cms.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddRouteContentEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Example",
                keyColumn: "Id",
                keyValue: new Guid("3509a9b9-c422-4d6a-9642-eb934967e97e"));

            migrationBuilder.CreateTable(
                name: "RouteContents",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ProjectId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    GroupId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    Title = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    DirectUrl = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    MetaTitle = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    MetaDescription = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    Keywords = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    Content = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CategoryId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DateCreated = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DateModified = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LanguageId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RouteContents", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RouteContents_RouteCategories_CategoryId",
                        column: x => x.CategoryId,
                        principalTable: "RouteCategories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "302dffb7-ba4e-44e2-b77a-1b258954f9d5",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "198081c8-9dce-4919-ae49-1eb3b6dc2554", "AQAAAAIAAYagAAAAEJ68TMP1IV8iwKuIxRZUS6itUlTkWFiuVk28Ep7XQf3LC0niGjLc3TXkCW8aQaf2dg==", "c9370fe2-1033-45d0-b0a7-8d0af70f3975" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "b28b07da-03a3-435a-a370-2a675c11a57d",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "3e93ffd1-bda7-4cb5-aa3f-4a430b764bd1", "AQAAAAIAAYagAAAAEE/L/5BQEiBlxsuvH5IEi5srSsXIiymkRqKVgJO4M31OdCgtxUDFwZEVjUqdVTzA3w==", "508eebd9-d6a0-44cc-93fa-7d65205f9ac6" });

            migrationBuilder.InsertData(
                table: "Example",
                columns: new[] { "Id", "Count", "DateCreated", "DateModified", "Description", "LanguageId", "Name" },
                values: new object[] { new Guid("d8d1362c-2629-41ea-bedd-0688a60e79f1"), 13, new DateTime(2025, 7, 5, 17, 23, 1, 466, DateTimeKind.Local).AddTicks(7720), new DateTime(2025, 7, 5, 17, 23, 1, 466, DateTimeKind.Local).AddTicks(7750), "The modelBuilder.Entity<Example>().HasData() method in Entity Framework is used to seed initial data into the database for a specific entity (in this case, the \"Example\" entity).", null, "First Data" });

            migrationBuilder.CreateIndex(
                name: "IX_RouteContents_CategoryId",
                table: "RouteContents",
                column: "CategoryId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "RouteContents");

            migrationBuilder.DeleteData(
                table: "Example",
                keyColumn: "Id",
                keyValue: new Guid("d8d1362c-2629-41ea-bedd-0688a60e79f1"));

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "302dffb7-ba4e-44e2-b77a-1b258954f9d5",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "6f0fa56e-fec6-486e-bc2d-e31586327621", "AQAAAAIAAYagAAAAENhUOj9bhsj7KCwtJOGzRlK6nCMRybUTFkrc8u/Kb9P86udzAYPabMpPD5as0yGFww==", "560e7edc-b862-4857-b01a-310e97e04a6b" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "b28b07da-03a3-435a-a370-2a675c11a57d",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "6e882266-0a8b-4f3e-9bc8-66ca22bdb53c", "AQAAAAIAAYagAAAAEBD6XynmiJIQXf/mbznsPnT1YxQQI7+HuuDTetIDtiqyBE6YN+pOwIbFm2w9yEwWYQ==", "45d0d808-a57f-42b9-8691-46cca8dbb469" });

            migrationBuilder.InsertData(
                table: "Example",
                columns: new[] { "Id", "Count", "DateCreated", "DateModified", "Description", "LanguageId", "Name" },
                values: new object[] { new Guid("3509a9b9-c422-4d6a-9642-eb934967e97e"), 13, new DateTime(2025, 7, 5, 14, 3, 9, 1, DateTimeKind.Local).AddTicks(3270), new DateTime(2025, 7, 5, 14, 3, 9, 1, DateTimeKind.Local).AddTicks(3300), "The modelBuilder.Entity<Example>().HasData() method in Entity Framework is used to seed initial data into the database for a specific entity (in this case, the \"Example\" entity).", null, "First Data" });
        }
    }
}
