using MediatR;
using Microsoft.AspNetCore.Mvc;
using TmobCore.Cms.Application.Features.RouteContent.Commands.CreateRouteContent;
using TmobCore.Cms.Application.Features.RouteContent.Commands.DeleteRouteContent;
using TmobCore.Cms.Application.Features.RouteContent.Commands.UpdateRouteContent;
using TmobCore.Cms.Application.Features.RouteContent.Queries.GetRouteContents;
using TmobCore.Cms.Application.Features.RouteContent.Queries.GetRouteContentById;

namespace TmobCore.Cms.Api.Controllers.v1
{
    /// <summary>
    /// Controller for managing route contents for the homepage "Popular Routes" section
    /// </summary>
    [Route("api/v1/[controller]")]
    [ApiController]
    public class RouteContentsController : ControllerBase
    {
        private readonly IMediator _mediator;

        public RouteContentsController(IMediator mediator)
        {
            _mediator = mediator;
        }

        /// <summary>
        /// Get all route contents with optional filtering
        /// </summary>
        /// <param name="query">Query parameters for filtering route contents</param>
        /// <returns>List of route contents</returns>
        /// <response code="200">Returns the list of route contents</response>
        /// <response code="400">If the request is invalid</response>
        /// <example>
        /// POST /api/v1/RouteContents
        /// {
        ///   "groupId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///   "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///   "categoryId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///   "status": 1,
        ///   "searchTerm": "travel",
        ///   "page": 1,
        ///   "pageSize": 10
        /// }
        /// </example>
        [HttpPost]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Get([FromBody] GetRouteContentsQuery query)
        {
            var response = await _mediator.Send(query);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        /// <summary>
        /// Get route content by ID
        /// </summary>
        /// <param name="id">Route content ID</param>
        /// <returns>Route content details</returns>
        /// <response code="200">Returns the route content</response>
        /// <response code="404">If the route content is not found</response>
        /// <response code="400">If the request is invalid</response>
        [HttpGet("{id}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GetById(Guid id)
        {
            var query = new GetRouteContentByIdQuery { Id = id };
            var response = await _mediator.Send(query);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return response.StatusCode == Models.Common.StatusCode.NotFound ? NotFound(response) : BadRequest(response);
        }

        /// <summary>
        /// Create new route contents
        /// </summary>
        /// <param name="command">Route content creation data</param>
        /// <returns>Success status</returns>
        /// <response code="200">If the route contents were created successfully</response>
        /// <response code="400">If the request is invalid</response>
        /// <example>
        /// POST /api/v1/RouteContents/create
        /// {
        ///   "routeContents": [
        ///     {
        ///       "title": "Popular Route to Paris",
        ///       "description": "Discover the best routes to Paris with amazing deals",
        ///       "directUrl": "https://example.com/paris-routes",
        ///       "metaTitle": "Best Paris Routes - Travel Deals",
        ///       "metaDescription": "Find the best travel routes to Paris with exclusive deals and offers",
        ///       "keywords": "paris, travel, routes, deals",
        ///       "content": "&lt;p&gt;Detailed content about Paris routes&lt;/p&gt;",
        ///       "categoryId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///       "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///       "status": 1
        ///     }
        ///   ]
        /// }
        /// </example>
        [HttpPost("create")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Create([FromBody] CreateRouteContentCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        /// <summary>
        /// Update existing route contents
        /// </summary>
        /// <param name="command">Route content update data</param>
        /// <returns>Success status</returns>
        /// <response code="200">If the route contents were updated successfully</response>
        /// <response code="400">If the request is invalid</response>
        /// <response code="404">If any route content is not found</response>
        /// <example>
        /// PUT /api/v1/RouteContents
        /// {
        ///   "routeContents": [
        ///     {
        ///       "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///       "title": "Updated Popular Route to Paris",
        ///       "description": "Updated description for the best routes to Paris",
        ///       "directUrl": "https://example.com/updated-paris-routes",
        ///       "metaTitle": "Updated Best Paris Routes - Travel Deals",
        ///       "metaDescription": "Updated meta description for Paris routes",
        ///       "keywords": "paris, travel, routes, deals, updated",
        ///       "content": "&lt;p&gt;Updated detailed content about Paris routes&lt;/p&gt;",
        ///       "categoryId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///       "languageId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///       "status": 1
        ///     }
        ///   ]
        /// }
        /// </example>
        [HttpPut]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> Update([FromBody] UpdateRouteContentCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return response.StatusCode == Models.Common.StatusCode.NotFound ? NotFound(response) : BadRequest(response);
        }

        /// <summary>
        /// Delete route contents by IDs (soft delete)
        /// </summary>
        /// <param name="command">List of route content IDs to delete</param>
        /// <returns>Number of deleted route contents</returns>
        /// <response code="200">Returns the number of deleted route contents</response>
        /// <response code="400">If the request is invalid or contains invalid IDs</response>
        /// <response code="404">If no route contents were found to delete</response>
        /// <example>
        /// DELETE /api/v1/RouteContents
        /// {
        ///   "ids": [
        ///     "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        ///     "3fa85f64-5717-4562-b3fc-2c963f66afa7"
        ///   ]
        /// }
        /// </example>
        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> Delete([FromBody] DeleteRouteContentCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return response.StatusCode == Models.Common.StatusCode.NotFound ? NotFound(response) : BadRequest(response);
        }
    }
}
