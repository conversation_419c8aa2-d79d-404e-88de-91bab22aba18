using MediatR;
using Microsoft.AspNetCore.Mvc;
using TmobCore.Cms.Application.Features.Cms.Commands.CreateCms;
using TmobCore.Cms.Application.Features.Cms.Commands.DeleteCms;
using TmobCore.Cms.Application.Features.Cms.Commands.UpdateCms;
using TmobCore.Cms.Application.Features.Cms.Queries.GetCms;
using TmobCore.Cms.Application.Features.Cms.Queries.GetCmsBySearchTerm;
using TmobCore.Cms.Application.Features.Cms.Queries.GetCmsKeyByLanguage;
using TmobCore.Cms.Application.Features.Cms.Queries.GetCmsKeyByLanguageCode;

namespace TmobCore.Cms.Api.Controllers.v1
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class CmsController : ControllerBase
    {
        private readonly IMediator _mediator;

        public CmsController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpPost("CreateCms")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> CreateCms([FromBody] CreateCmsCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        [HttpPut]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Put([FromBody] UpdateCmsCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete([FromBody] DeleteCmsCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        [HttpPost("GetCmsKeyByLanguageIdQuery")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GetCmsKeyByLanguageId([FromBody] GetCmsKeyByLanguageIdQuery query)
        {
            var response = await _mediator.Send(query);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Get()
        {
            var response = await _mediator.Send(new GetCmsQuery());
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        [HttpPost("GetCmsBySearchTerm")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GetCmsBySearchTerm([FromBody] GetCmsBySearchTermQuery query)
        {
            var response = await _mediator.Send(query);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        [HttpPost("GetCmsKeyByLanguageCode")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GetCmsKeyByLanguageCode([FromBody] GetCmsKeyByLanguageCodeQuery query)
        {
            var response = await _mediator.Send(query);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }


    }
}