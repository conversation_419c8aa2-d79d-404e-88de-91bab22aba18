using TmobCore.Cms.Domain.Common;
using TmobCore.Cms.Domain.Enums;

namespace TmobCore.Cms.Domain.Entities
{
    public class RouteContent : BaseEntity
    {
        public Guid ProjectId { get; set; }
        public Guid UserId { get; set; }
        public Guid GroupId { get; set; }
        public RouteCategoryStatus Status { get; set; } = RouteCategoryStatus.Draft;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string DirectUrl { get; set; } = string.Empty;
        public string MetaTitle { get; set; } = string.Empty;
        public string MetaDescription { get; set; } = string.Empty;
        public string Keywords { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public Guid CategoryId { get; set; }
        public RouteCategory Category { get; set; } = null!;
        public bool Deleted { get; set; } = false;
    }
}
