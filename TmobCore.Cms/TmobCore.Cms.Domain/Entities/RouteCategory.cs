using TmobCore.Cms.Domain.Common;
using TmobCore.Cms.Domain.Enums;

namespace TmobCore.Cms.Domain.Entities
{
    public class RouteCategory : BaseEntity
    {
        public Guid ProjectId { get; set; }
        public Guid UserId { get; set; }
        public Guid GroupId { get; set; }
        public RouteCategoryStatus Status { get; set; } = RouteCategoryStatus.Draft;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public bool Deleted { get; set; } = false;

        // Navigation property
        public virtual ICollection<RouteContent> RouteContents { get; set; } = new List<RouteContent>();
    }
}
