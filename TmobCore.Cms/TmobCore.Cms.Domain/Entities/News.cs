using TmobCore.Cms.Domain.Common;

namespace TmobCore.Cms.Domain.Entities;

public class News: BaseEntityExtended
{
    public Guid UserId { get; set; }
    public Guid ProjectId { get; set; }
    public Guid GroupId { get; set; }
    public string Title { get; set; }
    public string Slug { get; set; }
    public DateTime BeginDate { get; set; }
    public DateTime EndDate { get; set; }
    public Guid? ImageId { get; set; }
    public string Tags { get; set; } = string.Empty;
    public int ReadingTime { get; set; }
    public List<NewsContent> NewsContents { get; set; }
    
    public virtual Image Image { get; set; }
}