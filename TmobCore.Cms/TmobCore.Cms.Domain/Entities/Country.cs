using TmobCore.Cms.Domain.Common;

namespace TmobCore.Cms.Domain.Entities
{
    public class Country : BaseEntity
    {
        public Guid ProjectId { get; set; }
        public Guid UserId { get; set; }
        public Guid GroupId { get; set; }
        public string Name { get; set; }
        public string? Description { get; set; }
        public int Order { get; set; }
        public Guid? ImageId { get; set; }
        public List<City> Cities { get; set; }
        public bool IsDeleted { get; set; } = false;
        public BaseStatus Status { get; set; } = BaseStatus.Draft;
        public string Slug { get; set; } = String.Empty;
        
        public string? RegionCode { get; set; }
        
        public string? CountryCode { get; set; }
        
        public virtual Image Image { get; set; }
    }
}
